# PowerShell script to test SAP Cart API endpoint
# Usage: .\test-sap-cart-api.ps1

Write-Host "🛒 Testing SAP Cart API Endpoint" -ForegroundColor Green
Write-Host ""

# Configuration - Update these values for your environment
$SiteUrl = "http://localhost/your-wordpress-site"  # Update this URL
$Username = "your_username"                        # Your WordPress username
$Password = "your_password"                        # Your WordPress password (or Application Password)

$ApiEndpoint = "$SiteUrl/wp-json/wc/v3/sap-cart"
$TestEndpoint = "$SiteUrl/wp-json/wc/v3/sap-cart-test"

# Create credentials for Basic Auth
$base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $Username, $Password)))
$headers = @{
    "Authorization" = "Basic $base64AuthInfo"
    "Content-Type" = "application/json"
    "User-Agent" = "SAP-Cart-API-Test/1.0"
}

Write-Host "🔧 Configuration:" -ForegroundColor Yellow
Write-Host "Site URL: $SiteUrl" -ForegroundColor White
Write-Host "Username: $Username" -ForegroundColor White
Write-Host "API Endpoint: $ApiEndpoint" -ForegroundColor White
Write-Host "Test Endpoint: $TestEndpoint" -ForegroundColor White
Write-Host ""

# Test 1: Check if the test endpoint is working
Write-Host "🔍 Test 1: Testing API Availability..." -ForegroundColor Cyan

try {
    $testResponse = Invoke-RestMethod -Uri $TestEndpoint -Method Get -Headers $headers
    Write-Host "✅ Test endpoint is working!" -ForegroundColor Green
    Write-Host "Status: $($testResponse.status)" -ForegroundColor White
    Write-Host "Message: $($testResponse.message)" -ForegroundColor White
    Write-Host "Timestamp: $($testResponse.timestamp)" -ForegroundColor White
    Write-Host "WooCommerce Active: $($testResponse.woocommerce_active)" -ForegroundColor White
} catch {
    Write-Host "❌ Test endpoint failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check:" -ForegroundColor Yellow
    Write-Host "1. WordPress site URL is correct" -ForegroundColor White
    Write-Host "2. Username and password are correct" -ForegroundColor White
    Write-Host "3. SAP Cart plugin is active" -ForegroundColor White
    Write-Host "4. WordPress REST API is enabled" -ForegroundColor White
    exit 1
}

Write-Host ""

# Test 2: Send sample cart data
Write-Host "🛒 Test 2: Sending Sample Cart Data..." -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyyMMddHHmmss"

$testCartData = @{
    order = @{
        id = 0
        date_created = (Get-Date -Format "yyyy-MM-ddTHH:mm:ss")
        status = "cart"
        currency = "USD"
        total = "150.00"
        purchase_order = "TEST_PO_$timestamp"
        Cust_Req_Ship_Date = "2025-07-14"
        freight_terms = "C"
        shipping_conditions = "18"
        shipping_account = "*********"
        end_user = "john doe"
        end_use = "construction"
        billing = @{
            billing_id = "TEST_CUSTOMER_$timestamp"
            Z7_Partner_no = "Z7_TEST_$timestamp"
            first_name = "John"
            last_name = "Doe"
            company_code = "3090"
            company_name = "Grainger"
            address_1 = "123 Main St"
            address_2 = ""
            city = "Springfield"
            state = "IL"
            postcode = "62701"
            country = "US"
            email = "<EMAIL>"
            phone = "555-1234"
        }
        shipping = @{
            shipping_id = "TEST_CUSTOMER_$timestamp"
            first_name = "John"
            last_name = "Doe"
            company_name = "Grainger"
            address_1 = "123 Main St"
            address_2 = ""
            city = "Springfield"
            state = "IL"
            postcode = "62701"
            country = "US"
        }
        line_items = @(
            @{
                product_id = 987
                quantity = 2
            },
            @{
                product_id = 654
                quantity = 1
            }
        )
    }
} | ConvertTo-Json -Depth 10

Write-Host "Sending cart data..." -ForegroundColor White
Write-Host "Sample data structure:" -ForegroundColor Gray
Write-Host $testCartData -ForegroundColor Gray
Write-Host ""

try {
    $cartResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testCartData -Headers $headers
    Write-Host "✅ Cart data sent successfully!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor White
    Write-Host "Success: $($cartResponse.success)" -ForegroundColor White
    Write-Host "Message: $($cartResponse.message)" -ForegroundColor White
    Write-Host "Request ID: $($cartResponse.request_id)" -ForegroundColor White
    Write-Host "Timestamp: $($cartResponse.timestamp)" -ForegroundColor White
    Write-Host "Data Received: $($cartResponse.data_received)" -ForegroundColor White
} catch {
    Write-Host "❌ Cart data sending failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host ""

# Test 3: Test with invalid data
Write-Host "🧪 Test 3: Testing Error Handling..." -ForegroundColor Cyan

$invalidData = @{
    invalid = "data"
} | ConvertTo-Json

try {
    $errorResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $invalidData -Headers $headers
    Write-Host "⚠️ Unexpected success with invalid data" -ForegroundColor Yellow
    Write-Host "Response: $($errorResponse | ConvertTo-Json)" -ForegroundColor White
} catch {
    Write-Host "✅ Error handling working correctly" -ForegroundColor Green
    Write-Host "Expected error for invalid data format" -ForegroundColor White
}

Write-Host ""
Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "What the SAP Cart API does:" -ForegroundColor Yellow
Write-Host "1. ✅ Automatically triggers when users visit the cart page" -ForegroundColor White
Write-Host "2. ✅ Collects cart items, user data, and SAP customer information" -ForegroundColor White
Write-Host "3. ✅ Formats data into your specified JSON structure" -ForegroundColor White
Write-Host "4. ✅ Sends data to the SAP Cart API endpoint" -ForegroundColor White
Write-Host "5. ✅ Logs all activity to APIlogs-Cart.log" -ForegroundColor White
Write-Host ""
Write-Host "API Endpoint: $ApiEndpoint" -ForegroundColor Cyan
Write-Host "Test Endpoint: $TestEndpoint" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Visit your WordPress cart page while logged in" -ForegroundColor White
Write-Host "2. Check browser console for automatic data sending" -ForegroundColor White
Write-Host "3. Check wp-content/logs/APIlogs-Cart.log for API activity" -ForegroundColor White
Write-Host "4. Verify the JSON structure matches your requirements" -ForegroundColor White
Write-Host "5. Add any missing custom fields to user profiles" -ForegroundColor White
