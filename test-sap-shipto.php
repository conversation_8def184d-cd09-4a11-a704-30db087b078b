<?php
/**
 * Test script for SAP ShipTo Addresses API
 * This script tests the plugin functionality with sample data
 */

// Load WordPress
if (!function_exists('get_user_meta')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>SAP ShipTo Addresses API Test</h1>\n";

// Test data with leading zeros to test trimming functionality
$test_data = [
    "shipTo" => [
        [
            "addressID" => "0001128545",  // Test addressID with leading zeros
            "customerId" => "0001126769", // Test customerId with leading zeros
            "companyCode" => "1000",
            "identifier" => "Main Warehouse",
            "isDefaultAddress" => true,
            "companyName" => null,
            "country" => "US",
            "address" => [
                "street" => "Elm Street",
                "apartment" => null,
                "city" => "Springwood",
                "postalCode" => "12345"
            ],
            "stateCounty" => "NY"
        ],
        [
            "addressID" => "0000000999",  // Another addressID with leading zeros
            "customerId" => "0001126769", // Same customer, different address
            "companyCode" => "1000",
            "identifier" => "Secondary Warehouse",
            "isDefaultAddress" => false,
            "companyName" => null,
            "country" => "US",
            "address" => [
                "street" => "Pine Road",
                "houseNumber" => "99B",
                "apartment" => "Unit 5",
                "city" => "Springwood",
                "postalCode" => "12346"
            ]
        ]
    ]
];

echo "<h2>Test Data</h2>\n";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>\n";

// Check if we have a test user with trimmed customerId (1126769)
echo "<h2>Checking for Test User</h2>\n";
echo "<p><strong>Note:</strong> Looking for user with trimmed customer ID '1126769' (from '0001126769')</p>\n";

$trimmed_customer_id = '1126769'; // This is what 0001126769 becomes after trimming

$test_user = get_users([
    'meta_key' => '_customer',
    'meta_value' => $trimmed_customer_id,
    'number' => 1
]);

if (empty($test_user)) {
    echo "<p>❌ No user found with customerId '{$trimmed_customer_id}'</p>\n";
    echo "<p>Creating a test user...</p>\n";

    // Create test user
    $user_data = [
        'user_login' => 'testuser_' . time(),
        'user_email' => 'test_' . time() . '@example.com',
        'user_pass' => wp_generate_password(),
        'role' => 'b2b_customer'
    ];

    $user_id = wp_insert_user($user_data);

    if (is_wp_error($user_id)) {
        echo "<p>❌ Failed to create test user: " . $user_id->get_error_message() . "</p>\n";
        exit;
    }

    // Add customer ID meta (trimmed version)
    update_user_meta($user_id, '_customer', $trimmed_customer_id);

    echo "<p>✅ Created test user with ID: {$user_id} and customer ID: {$trimmed_customer_id}</p>\n";
} else {
    $user_id = $test_user[0]->ID;
    echo "<p>✅ Found existing test user with ID: {$user_id}</p>\n";
}

// Show current addresses before processing
echo "<h2>Current Addresses (Before)</h2>\n";
$current_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
if (empty($current_addresses)) {
    echo "<p>No existing addresses</p>\n";
} else {
    echo "<pre>" . print_r($current_addresses, true) . "</pre>\n";
}

// Test the conversion function
echo "<h2>Testing Address Conversion</h2>\n";

// Load the plugin functions
if (function_exists('convert_sap_to_wcmca_format')) {
    foreach ($test_data['shipTo'] as $index => $sap_address) {
        echo "<h3>Address " . ($index + 1) . "</h3>\n";
        echo "<h4>SAP Format:</h4>\n";
        echo "<pre>" . print_r($sap_address, true) . "</pre>\n";
        
        $wcmca_format = convert_sap_to_wcmca_format($sap_address);
        
        echo "<h4>WCMCA Format:</h4>\n";
        echo "<pre>" . print_r($wcmca_format, true) . "</pre>\n";
    }
} else {
    echo "<p>❌ Plugin functions not loaded. Make sure the plugin is activated.</p>\n";
}

// Test the API endpoint via internal function call
echo "<h2>Testing API Processing (Replace Mode)</h2>\n";
echo "<p><strong>Note:</strong> The plugin now replaces ALL existing addresses for each customer instead of adding to them.</p>\n";

if (function_exists('process_customer_shipto_addresses')) {
    // Group addresses by customer ID (same as the plugin does)
    $customers_addresses = [];
    foreach ($test_data['shipTo'] as $address) {
        $customer_id = $address['customerId'];
        if (!isset($customers_addresses[$customer_id])) {
            $customers_addresses[$customer_id] = [];
        }
        $customers_addresses[$customer_id][] = $address;
    }

    foreach ($customers_addresses as $customer_id => $addresses) {
        echo "<h3>Processing All Addresses for Customer: {$customer_id}</h3>\n";
        echo "<p>Replacing existing addresses with " . count($addresses) . " new addresses</p>\n";

        $result = process_customer_shipto_addresses($customer_id, $addresses);

        if (is_wp_error($result)) {
            echo "<p>❌ Error: " . $result->get_error_message() . "</p>\n";
        } else {
            echo "<p>✅ Success: " . print_r($result, true) . "</p>\n";
        }
    }
} else {
    echo "<p>❌ Plugin processing functions not loaded.</p>\n";
}

// Show final addresses after processing
echo "<h2>Final Addresses (After)</h2>\n";
$final_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
if (empty($final_addresses)) {
    echo "<p>No addresses found</p>\n";
} else {
    echo "<pre>" . print_r($final_addresses, true) . "</pre>\n";
    echo "<p>Total addresses: " . count($final_addresses) . "</p>\n";
}

// Test the REST API endpoint
echo "<h2>Testing REST API Endpoint</h2>\n";
echo "<p>You can test the REST API endpoint by sending a POST request to:</p>\n";
echo "<code>" . home_url('/wp-json/wc/v3/sap-shipto') . "</code>\n";
echo "<p>With the following JSON data:</p>\n";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>\n";

echo "<h2>cURL Command for Testing</h2>\n";
echo "<pre>";
echo "curl -X POST \\\n";
echo "  '" . home_url('/wp-json/wc/v3/sap-shipto') . "' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($test_data) . "'";
echo "</pre>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p>Check the WordPress error logs for detailed processing information.</p>\n";
?>
