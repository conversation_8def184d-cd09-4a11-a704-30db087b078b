<?php
/**
 * Plugin Name: SAP Cart API
 * Plugin URI: https://atakinteractive.com
 * Description: Sends cart data to SAP endpoint whenever cart page is accessed.
 * Version: 1.0
 * Author: ATAK Interactive
 * Author URI: https://atakinteractive.com
 * Requires at least: 5.0
 * Tested up to: 6.7
 * Requires PHP: 7.4
 * Network: false
 * License: GPL v2 or later
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Debug: Log when plugin loads
error_log( "🛒 SAP-Cart plugin loaded at " . current_time( 'mysql' ) );

// Hook into cart page visits
add_action( 'woocommerce_before_cart', 'sap_cart_trigger_on_cart_page' );
add_action( 'wp_enqueue_scripts', 'sap_cart_enqueue_scripts' );

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-cart', [
        'methods'             => 'POST',
        'callback'            => 'sap_cart_endpoint',
        'permission_callback' => 'sap_cart_permission_check',
    ] );

    // Add a simple GET endpoint for testing
    register_rest_route( 'wc/v3', '/sap-cart-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            error_log( "🔍 SAP-Cart TEST endpoint called" );
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP Cart API is active',
                'timestamp' => current_time( 'mysql' ),
                'wordpress_version' => get_bloginfo( 'version' ),
                'woocommerce_active' => class_exists( 'WooCommerce' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true', // Keep test endpoint open
    ] );
} );

/**
 * Permission callback for SAP Cart API
 * Uses WordPress REST API authentication (same as SAP SoldTo)
 */
function sap_cart_permission_check( WP_REST_Request $request ) {
    $request_id = uniqid( 'sap_cart_auth_' );

    error_log( "🔍 SAP Cart API: Checking WordPress REST API authentication" );

    // Log authentication attempt details
    sap_cart_log_api( "🔐 SAP-Cart AUTH CHECK START [ID: {$request_id}]" );
    sap_cart_log_api( "📡 Auth Request Method: " . $request->get_method() );
    sap_cart_log_api( "📡 Auth Request URL: " . $request->get_route() );
    sap_cart_log_api( "📡 Auth Request Time: " . current_time( 'mysql' ) );
    sap_cart_log_api( "📡 Auth User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_cart_log_api( "📡 Auth Client IP: " . sap_cart_get_client_ip() );

    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();

    if ( ! $user || ! $user->ID ) {
        $error_response = [
            'code' => 'rest_not_logged_in',
            'message' => 'You are not currently logged in.',
            'data' => [ 'status' => 401 ]
        ];

        // Log the unsuccessful authentication attempt
        error_log( "❌ SAP Cart API: No authenticated user found" );
        sap_cart_log_api( "❌ SAP-Cart AUTH FAILED [ID: {$request_id}]: " . wp_json_encode( $error_response, JSON_PRETTY_PRINT ) );
        sap_cart_log_api( "🚫 SAP-Cart Authentication Error: No authenticated user found" );

        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }

    error_log( "🔍 SAP Cart API: Authenticated user: {$user->ID} ({$user->user_login})" );
    sap_cart_log_api( "🔐 SAP-Cart Authenticated User [ID: {$request_id}]: {$user->ID} ({$user->user_login})" );

    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP Cart API: User has capability: {$cap}" );
            sap_cart_log_api( "✅ SAP-Cart AUTH SUCCESS [ID: {$request_id}]: User {$user->ID} has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];

    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP Cart API: User has allowed role: {$role}" );
            sap_cart_log_api( "✅ SAP-Cart AUTH SUCCESS [ID: {$request_id}]: User {$user->ID} has allowed role: {$role}" );
            return true;
        }
    }

    // User is authenticated but lacks sufficient permissions
    $error_response = [
        'code' => 'rest_forbidden',
        'message' => 'Sorry, you are not allowed to access this endpoint.',
        'data' => [ 'status' => 403 ]
    ];

    error_log( "❌ SAP Cart API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    sap_cart_log_api( "❌ SAP-Cart AUTH FAILED [ID: {$request_id}]: " . wp_json_encode( $error_response, JSON_PRETTY_PRINT ) );
    sap_cart_log_api( "🚫 SAP-Cart Permission Error: User {$user->ID} ({$user->user_login}) lacks sufficient permissions. User roles: " . implode( ', ', $user_roles ) );

    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}

/**
 * Main SAP Cart API endpoint
 */
function sap_cart_endpoint( WP_REST_Request $request ) {
    $start_time = microtime( true );
    $request_id = uniqid( 'sap_cart_' );

    // Log API call start with comprehensive details
    sap_cart_log_api( "🚀 SAP-Cart API CALL START [ID: {$request_id}]" );
    sap_cart_log_api( "📡 Request Method: " . $request->get_method() );
    sap_cart_log_api( "📡 Request URL: " . $request->get_route() );
    sap_cart_log_api( "📡 Request Time: " . current_time( 'mysql' ) );
    sap_cart_log_api( "📡 User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_cart_log_api( "📡 Client IP: " . sap_cart_get_client_ip() );

    // Log authentication details
    $current_user = wp_get_current_user();
    if ( $current_user && $current_user->ID ) {
        sap_cart_log_api( "🔐 Authenticated User: {$current_user->ID} ({$current_user->user_login})" );
    }

    $data = $request->get_json_params();

    // Log the incoming data (sanitized for security)
    $sanitized_data = sap_cart_sanitize_log_data( $data );
    sap_cart_log_api( "📥 SAP-Cart Request Data [ID: {$request_id}]: " . wp_json_encode( $sanitized_data, JSON_PRETTY_PRINT ) );

    // Validation
    if ( empty( $data ) || ! is_array( $data ) ) {
        $error_response = [
            'code' => 'invalid_data',
            'message' => 'No valid JSON data received',
            'data' => [ 'status' => 400 ]
        ];

        error_log( "❌ No valid JSON data received" );
        sap_cart_log_api( "❌ SAP-Cart VALIDATION ERROR [ID: {$request_id}]: " . wp_json_encode( $error_response, JSON_PRETTY_PRINT ) );
        sap_cart_log_api( "🚫 SAP-Cart Request failed: No valid JSON data received" );

        return new WP_Error( 'invalid_data', 'No valid JSON data received', [ 'status'=>400 ] );
    }

    // Process the cart data - ADD YOUR PROCESSING LOGIC HERE
    $processing_result = sap_cart_process_data( $data, $request_id );

    if ( is_wp_error( $processing_result ) ) {
        sap_cart_log_api( "❌ SAP-Cart PROCESSING ERROR [ID: {$request_id}]: " . $processing_result->get_error_message() );
        return $processing_result;
    }

    $response = [
        'success' => true,
        'message' => 'Cart data received and processed successfully',
        'timestamp' => current_time( 'mysql' ),
        'request_id' => $request_id,
        'data_received' => count( $data ),
        'processing_result' => $processing_result
    ];

    // Log API call completion
    $end_time = microtime( true );
    $execution_time = round( ( $end_time - $start_time ) * 1000, 2 ); // Convert to milliseconds

    sap_cart_log_api( "📤 SAP-Cart Response [ID: {$request_id}]: " . wp_json_encode( $response, JSON_PRETTY_PRINT ) );
    sap_cart_log_api( "⏱️ SAP-Cart API CALL COMPLETE [ID: {$request_id}] - Execution Time: {$execution_time}ms" );
    sap_cart_log_api( "✅ SAP-Cart Success: {$response['success']}, Request ID: {$response['request_id']}" );

    return rest_ensure_response( $response );
}

/**
 * Trigger cart data collection when cart page is accessed
 */
function sap_cart_trigger_on_cart_page() {
    error_log( "🛒 SAP Cart: Cart page accessed, triggering data collection" );

    // Only trigger for logged-in users
    if ( ! is_user_logged_in() ) {
        error_log( "🛒 SAP Cart: User not logged in, skipping cart data collection" );
        return;
    }

    // Collect cart data
    $cart_data = sap_cart_collect_data();

    if ( $cart_data ) {
        // Send data to our own endpoint (this will be handled via AJAX)
        sap_cart_log_api( "🛒 SAP Cart: Cart data collected for user " . get_current_user_id() );

        // Add JavaScript to send data via AJAX
        add_action( 'wp_footer', 'sap_cart_add_ajax_script' );
    } else {
        error_log( "🛒 SAP Cart: No cart data to send" );
    }
}

/**
 * Enqueue scripts for cart page
 */
function sap_cart_enqueue_scripts() {
    if ( is_cart() ) {
        wp_enqueue_script( 'jquery' );
    }
}

/**
 * Add AJAX script to send cart data
 */
function sap_cart_add_ajax_script() {
    if ( ! is_cart() ) {
        return;
    }

    $cart_data = sap_cart_collect_data();
    if ( ! $cart_data ) {
        return;
    }

    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        console.log('🛒 SAP Cart: Sending cart data to endpoint');

        var cartData = <?php echo wp_json_encode( $cart_data ); ?>;

        $.ajax({
            url: '<?php echo rest_url( 'wc/v3/sap-cart' ); ?>',
            type: 'POST',
            data: JSON.stringify(cartData),
            contentType: 'application/json',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', '<?php echo wp_create_nonce( 'wp_rest' ); ?>');
            },
            success: function(response) {
                console.log('🛒 SAP Cart: Data sent successfully', response);
            },
            error: function(xhr, status, error) {
                console.error('🛒 SAP Cart: Error sending data', error);
                console.error('Response:', xhr.responseText);
            }
        });
    });
    </script>
    <?php
}

/**
 * Collect cart and user data
 */
function sap_cart_collect_data() {
    if ( ! function_exists( 'WC' ) || ! WC()->cart ) {
        return false;
    }

    $current_user = wp_get_current_user();
    if ( ! $current_user || ! $current_user->ID ) {
        return false;
    }

    // Get cart items
    $cart_items = [];
    foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
        $product = $cart_item['data'];
        $cart_items[] = [
            'product_id' => $cart_item['product_id'],
            'quantity' => $cart_item['quantity'],
        ];
    }

    // Get SAP customer data from database
    $sap_customer_data = sap_cart_get_customer_data( $current_user->ID );

    // Get user metadata
    $user_meta = get_user_meta( $current_user->ID );

    // Build the JSON structure
    $order_data = [
        'order' => [
            'id' => 0, // No order ID since this is cart data
            'date_created' => current_time( 'c' ), // ISO 8601 format
            'status' => 'cart', // Custom status for cart
            'currency' => get_woocommerce_currency(),
            'total' => WC()->cart->get_total( 'edit' ),
            'purchase_order' => $user_meta['purchase_order'][0] ?? '',
            'Cust_Req_Ship_Date' => $user_meta['Cust_Req_Ship_Date'][0] ?? '',
            'freight_terms' => $user_meta['freight_terms'][0] ?? '',
            'shipping_conditions' => $user_meta['shipping_conditions'][0] ?? '',
            'shipping_account' => $user_meta['shipping_account'][0] ?? '',
            'end_user' => $user_meta['end_user'][0] ?? '',
            'end_use' => $user_meta['end_use'][0] ?? '',
            'billing' => sap_cart_get_billing_data( $current_user, $sap_customer_data, $user_meta ),
            'shipping' => sap_cart_get_shipping_data( $current_user, $user_meta ),
            'line_items' => $cart_items,
        ]
    ];

    return $order_data;
}

/**
 * Write to custom API log file
 */
function sap_cart_log_api( $message ) {
    $log_dir = WP_CONTENT_DIR . '/logs';
    $log_file = $log_dir . '/APIlogs-Cart.log';

    // Create logs directory if it doesn't exist
    if ( ! file_exists( $log_dir ) ) {
        wp_mkdir_p( $log_dir );
    }

    // Format log entry with timestamp
    $timestamp = current_time( 'Y-m-d H:i:s' );
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;

    // Write to log file
    file_put_contents( $log_file, $log_entry, FILE_APPEND | LOCK_EX );

    // Also log to WordPress error log for backup (optional)
    error_log( $message );
}

/**
 * Get client IP address for logging
 */
function sap_cart_get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

    foreach ( $ip_keys as $key ) {
        if ( ! empty( $_SERVER[$key] ) ) {
            $ip = sanitize_text_field( $_SERVER[$key] );
            // Handle comma-separated IPs (from proxies)
            if ( strpos( $ip, ',' ) !== false ) {
                $ip = trim( explode( ',', $ip )[0] );
            }
            if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                return $ip;
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
}

/**
 * Sanitize data for logging (remove sensitive information)
 */
function sap_cart_sanitize_log_data( $data ) {
    if ( ! is_array( $data ) ) {
        return $data;
    }

    $sanitized = $data;

    // Remove or mask sensitive fields
    $sensitive_fields = ['password', 'token', 'secret', 'key'];

    array_walk_recursive( $sanitized, function( &$value, $key ) use ( $sensitive_fields ) {
        if ( in_array( strtolower( $key ), $sensitive_fields ) ) {
            $value = '[REDACTED]';
        }
        // Mask email addresses partially
        if ( strtolower( $key ) === 'email' && is_string( $value ) && strpos( $value, '@' ) !== false ) {
            $parts = explode( '@', $value );
            if ( count( $parts ) === 2 ) {
                $username = $parts[0];
                $domain = $parts[1];
                $masked_username = substr( $username, 0, 2 ) . str_repeat( '*', max( 0, strlen( $username ) - 2 ) );
                $value = $masked_username . '@' . $domain;
            }
        }
    });

    return $sanitized;
}

/**
 * Get SAP customer data from database
 */
function sap_cart_get_customer_data( $user_id ) {
    global $wpdb;

    // Get customer ID from user meta
    $customer_id = get_user_meta( $user_id, '_customer', true );

    if ( empty( $customer_id ) ) {
        return null;
    }

    $table_name = $wpdb->prefix . 'sap_soldto_customers';

    // Check if table exists
    if ( $wpdb->get_var( "SHOW TABLES LIKE '{$table_name}'" ) != $table_name ) {
        return null;
    }

    $sap_customer = $wpdb->get_row( $wpdb->prepare(
        "SELECT customer_id, company_code, country_code, price_group, Z7_Partner_no, company, address_line1, address_line2, city, postcode, country_region, state_county FROM {$table_name} WHERE customer_id = %s",
        $customer_id
    ) );

    return $sap_customer;
}

/**
 * Get billing data
 */
function sap_cart_get_billing_data( $user, $sap_customer_data, $user_meta ) {
    return [
        'billing_id' => $sap_customer_data->customer_id ?? '',
        'Z7_Partner_no' => $sap_customer_data->Z7_Partner_no ?? '',
        'first_name' => $user_meta['billing_first_name'][0] ?? $user_meta['first_name'][0] ?? '',
        'last_name' => $user_meta['billing_last_name'][0] ?? $user_meta['last_name'][0] ?? '',
        'company_code' => $sap_customer_data->company_code ?? '',
        'company_name' => $sap_customer_data->company ?? $user_meta['billing_company'][0] ?? '',
        'address_1' => $sap_customer_data->address_line1 ?? $user_meta['billing_address_1'][0] ?? '',
        'address_2' => $sap_customer_data->address_line2 ?? $user_meta['billing_address_2'][0] ?? '',
        'city' => $sap_customer_data->city ?? $user_meta['billing_city'][0] ?? '',
        'state' => $sap_customer_data->state_county ?? $user_meta['billing_state'][0] ?? '',
        'postcode' => $sap_customer_data->postcode ?? $user_meta['billing_postcode'][0] ?? '',
        'country' => $sap_customer_data->country_region ?? $user_meta['billing_country'][0] ?? '',
        'email' => $user->user_email,
        'phone' => $user_meta['billing_phone'][0] ?? '',
    ];
}

/**
 * Get shipping data
 */
function sap_cart_get_shipping_data( $user, $user_meta ) {
    return [
        'shipping_id' => $user_meta['_customer'][0] ?? '',
        'first_name' => $user_meta['shipping_first_name'][0] ?? $user_meta['first_name'][0] ?? '',
        'last_name' => $user_meta['shipping_last_name'][0] ?? $user_meta['last_name'][0] ?? '',
        'company_name' => $user_meta['shipping_company'][0] ?? '',
        'address_1' => $user_meta['shipping_address_1'][0] ?? '',
        'address_2' => $user_meta['shipping_address_2'][0] ?? '',
        'city' => $user_meta['shipping_city'][0] ?? '',
        'state' => $user_meta['shipping_state'][0] ?? '',
        'postcode' => $user_meta['shipping_postcode'][0] ?? '',
        'country' => $user_meta['shipping_country'][0] ?? '',
    ];
}

/**
 * Process the incoming cart data
 * ADD YOUR CUSTOM PROCESSING LOGIC HERE
 */
function sap_cart_process_data( $data, $request_id ) {
    sap_cart_log_api( "🔄 SAP-Cart PROCESSING START [ID: {$request_id}]" );

    // Validate that we have order data
    if ( empty( $data['order'] ) ) {
        return new WP_Error( 'missing_order_data', 'Order data is required', [ 'status' => 400 ] );
    }

    $order_data = $data['order'];

    // Log the key data points
    sap_cart_log_api( "📊 SAP-Cart Processing Order Data:" );
    sap_cart_log_api( "   - Order ID: " . ( $order_data['id'] ?? 'N/A' ) );
    sap_cart_log_api( "   - Status: " . ( $order_data['status'] ?? 'N/A' ) );
    sap_cart_log_api( "   - Total: " . ( $order_data['total'] ?? 'N/A' ) );
    sap_cart_log_api( "   - Currency: " . ( $order_data['currency'] ?? 'N/A' ) );
    sap_cart_log_api( "   - Line Items: " . count( $order_data['line_items'] ?? [] ) );

    // Log billing information
    if ( ! empty( $order_data['billing'] ) ) {
        $billing = $order_data['billing'];
        sap_cart_log_api( "💳 SAP-Cart Billing Info:" );
        sap_cart_log_api( "   - Billing ID: " . ( $billing['billing_id'] ?? 'N/A' ) );
        sap_cart_log_api( "   - Z7_Partner_no: " . ( $billing['Z7_Partner_no'] ?? 'N/A' ) );
        sap_cart_log_api( "   - Company Code: " . ( $billing['company_code'] ?? 'N/A' ) );
        sap_cart_log_api( "   - Company Name: " . ( $billing['company_name'] ?? 'N/A' ) );
        sap_cart_log_api( "   - Email: " . ( $billing['email'] ?? 'N/A' ) );
    }

    // Log shipping information
    if ( ! empty( $order_data['shipping'] ) ) {
        $shipping = $order_data['shipping'];
        sap_cart_log_api( "🚚 SAP-Cart Shipping Info:" );
        sap_cart_log_api( "   - Shipping ID: " . ( $shipping['shipping_id'] ?? 'N/A' ) );
        sap_cart_log_api( "   - Company: " . ( $shipping['company_name'] ?? 'N/A' ) );
        sap_cart_log_api( "   - Address: " . ( $shipping['address_1'] ?? 'N/A' ) );
        sap_cart_log_api( "   - City: " . ( $shipping['city'] ?? 'N/A' ) );
    }

    // Log line items
    if ( ! empty( $order_data['line_items'] ) ) {
        sap_cart_log_api( "📦 SAP-Cart Line Items:" );
        foreach ( $order_data['line_items'] as $index => $item ) {
            sap_cart_log_api( "   - Item " . ( $index + 1 ) . ": Product ID " . ( $item['product_id'] ?? 'N/A' ) . ", Qty: " . ( $item['quantity'] ?? 'N/A' ) );
        }
    }

    // Save to sap_cart database table
    $save_result = sap_cart_save_to_database( $order_data, $request_id );

    if ( is_wp_error( $save_result ) ) {
        return $save_result;
    }

    sap_cart_log_api( "💾 SAP-Cart Data saved to database with ID: " . $save_result['record_id'] );

    /*
    // Example: Send to external API
    $external_api_url = 'https://your-sap-system.com/api/cart-data';
    $response = wp_remote_post( $external_api_url, [
        'body' => wp_json_encode( $data ),
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer your-api-token'
        ]
    ] );

    if ( is_wp_error( $response ) ) {
        return new WP_Error( 'external_api_failed', 'Failed to send data to external API: ' . $response->get_error_message(), [ 'status' => 500 ] );
    }
    */

    sap_cart_log_api( "✅ SAP-Cart PROCESSING COMPLETE [ID: {$request_id}]" );

    return [
        'processed' => true,
        'items_processed' => count( $order_data['line_items'] ?? [] ),
        'processing_time' => current_time( 'mysql' ),
        'database_record_id' => $save_result['record_id']
    ];
}

/**
 * Save cart data to sap_cart database table
 */
function sap_cart_save_to_database( $order_data, $request_id ) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sap_cart';

    // Check if table exists
    if ( $wpdb->get_var( "SHOW TABLES LIKE '{$table_name}'" ) != $table_name ) {
        return new WP_Error( 'table_not_found', 'SAP Cart table does not exist. Please run create-sap-cart-table.sql', [ 'status' => 500 ] );
    }

    // Get current user info
    $current_user = wp_get_current_user();

    // Extract billing data
    $billing = $order_data['billing'] ?? [];

    // Extract shipping data
    $shipping = $order_data['shipping'] ?? [];

    // Prepare line items JSON
    $line_items = $order_data['line_items'] ?? [];
    $line_items_json = wp_json_encode( $line_items );
    $line_items_count = count( $line_items );

    // Parse date fields
    $cust_req_ship_date = null;
    if ( ! empty( $order_data['Cust_Req_Ship_Date'] ) ) {
        $date = DateTime::createFromFormat( 'Y-m-d', $order_data['Cust_Req_Ship_Date'] );
        if ( $date ) {
            $cust_req_ship_date = $date->format( 'Y-m-d' );
        }
    }

    // Prepare database record
    $db_data = [
        // Request tracking
        'request_id' => $request_id,

        // Order basic info
        'order_id' => intval( $order_data['id'] ?? 0 ),
        'date_created' => current_time( 'mysql' ),
        'order_status' => sanitize_text_field( $order_data['status'] ?? 'cart' ),
        'currency' => sanitize_text_field( $order_data['currency'] ?? 'USD' ),
        'total' => floatval( $order_data['total'] ?? 0 ),

        // Custom order fields
        'purchase_order' => sanitize_text_field( $order_data['purchase_order'] ?? '' ),
        'cust_req_ship_date' => $cust_req_ship_date,
        'freight_terms' => sanitize_text_field( $order_data['freight_terms'] ?? '' ),
        'shipping_conditions' => sanitize_text_field( $order_data['shipping_conditions'] ?? '' ),
        'shipping_account' => sanitize_text_field( $order_data['shipping_account'] ?? '' ),
        'end_user' => sanitize_text_field( $order_data['end_user'] ?? '' ),
        'end_use' => sanitize_text_field( $order_data['end_use'] ?? '' ),

        // Billing information
        'billing_id' => sanitize_text_field( $billing['billing_id'] ?? '' ),
        'billing_z7_partner_no' => sanitize_text_field( $billing['Z7_Partner_no'] ?? '' ),
        'billing_first_name' => sanitize_text_field( $billing['first_name'] ?? '' ),
        'billing_last_name' => sanitize_text_field( $billing['last_name'] ?? '' ),
        'billing_company_code' => sanitize_text_field( $billing['company_code'] ?? '' ),
        'billing_company_name' => sanitize_text_field( $billing['company_name'] ?? '' ),
        'billing_address_1' => sanitize_text_field( $billing['address_1'] ?? '' ),
        'billing_address_2' => sanitize_text_field( $billing['address_2'] ?? '' ),
        'billing_city' => sanitize_text_field( $billing['city'] ?? '' ),
        'billing_state' => sanitize_text_field( $billing['state'] ?? '' ),
        'billing_postcode' => sanitize_text_field( $billing['postcode'] ?? '' ),
        'billing_country' => sanitize_text_field( $billing['country'] ?? '' ),
        'billing_email' => sanitize_email( $billing['email'] ?? '' ),
        'billing_phone' => sanitize_text_field( $billing['phone'] ?? '' ),

        // Shipping information
        'shipping_id' => sanitize_text_field( $shipping['shipping_id'] ?? '' ),
        'shipping_first_name' => sanitize_text_field( $shipping['first_name'] ?? '' ),
        'shipping_last_name' => sanitize_text_field( $shipping['last_name'] ?? '' ),
        'shipping_company_name' => sanitize_text_field( $shipping['company_name'] ?? '' ),
        'shipping_address_1' => sanitize_text_field( $shipping['address_1'] ?? '' ),
        'shipping_address_2' => sanitize_text_field( $shipping['address_2'] ?? '' ),
        'shipping_city' => sanitize_text_field( $shipping['city'] ?? '' ),
        'shipping_state' => sanitize_text_field( $shipping['state'] ?? '' ),
        'shipping_postcode' => sanitize_text_field( $shipping['postcode'] ?? '' ),
        'shipping_country' => sanitize_text_field( $shipping['country'] ?? '' ),

        // Line items
        'line_items' => $line_items_json,
        'line_items_count' => $line_items_count,

        // WordPress user info
        'wp_user_id' => $current_user->ID ?? null,
        'wp_user_login' => $current_user->user_login ?? '',

        // Processing status
        'processing_status' => 'received',
        'processing_notes' => 'Cart data received and processed successfully',
        'processed_at' => current_time( 'mysql' ),

        // Raw data backup
        'raw_json_data' => wp_json_encode( $order_data ),

        // Timestamps
        'created_at' => current_time( 'mysql' ),
        'updated_at' => current_time( 'mysql' )
    ];

    // Insert into database
    $result = $wpdb->insert(
        $table_name,
        $db_data,
        [
            '%s', '%d', '%s', '%s', '%s', '%f', '%s', '%s', '%s', '%s', '%s', '%s', '%s',
            '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s',
            '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s',
            '%s', '%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s'
        ]
    );

    if ( $result === false ) {
        sap_cart_log_api( "❌ SAP-Cart Database insert failed: " . $wpdb->last_error );
        return new WP_Error( 'db_insert_failed', 'Failed to save cart data to database: ' . $wpdb->last_error, [ 'status' => 500 ] );
    }

    $record_id = $wpdb->insert_id;
    sap_cart_log_api( "✅ SAP-Cart Database record created with ID: {$record_id}" );

    return [
        'record_id' => $record_id,
        'action' => 'created',
        'table' => $table_name
    ];
}
