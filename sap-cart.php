<?php
/**
 * Plugin Name: SAP Cart API
 * Plugin URI: https://atakinteractive.com
 * Description: Sends cart data to SAP endpoint whenever cart page is accessed.
 * Version: 1.0
 * Author: ATAK Interactive
 * Author URI: https://atakinteractive.com
 * Requires at least: 5.0
 * Tested up to: 6.7
 * Requires PHP: 7.4
 * Network: false
 * License: GPL v2 or later
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Debug: Log when plugin loads
error_log( "🛒 SAP-Cart plugin loaded at " . current_time( 'mysql' ) );

// Hook into cart page visits
add_action( 'woocommerce_before_cart', 'sap_cart_trigger_on_cart_page' );
add_action( 'wp_enqueue_scripts', 'sap_cart_enqueue_scripts' );

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-cart', [
        'methods'             => 'POST',
        'callback'            => 'sap_cart_endpoint',
        'permission_callback' => 'sap_cart_permission_check',
    ] );

    // Add a simple GET endpoint for testing
    register_rest_route( 'wc/v3', '/sap-cart-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            error_log( "🔍 SAP-Cart TEST endpoint called" );
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP Cart API is active',
                'timestamp' => current_time( 'mysql' ),
                'wordpress_version' => get_bloginfo( 'version' ),
                'woocommerce_active' => class_exists( 'WooCommerce' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true', // Keep test endpoint open
    ] );
} );

/**
 * Permission callback for SAP Cart API
 * Uses WordPress REST API authentication (same as SAP SoldTo)
 */
function sap_cart_permission_check( WP_REST_Request $request ) {
    $request_id = uniqid( 'sap_cart_auth_' );

    error_log( "🔍 SAP Cart API: Checking WordPress REST API authentication" );

    // Log authentication attempt details
    sap_cart_log_api( "🔐 SAP-Cart AUTH CHECK START [ID: {$request_id}]" );
    sap_cart_log_api( "📡 Auth Request Method: " . $request->get_method() );
    sap_cart_log_api( "📡 Auth Request URL: " . $request->get_route() );
    sap_cart_log_api( "📡 Auth Request Time: " . current_time( 'mysql' ) );
    sap_cart_log_api( "📡 Auth User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_cart_log_api( "📡 Auth Client IP: " . sap_cart_get_client_ip() );

    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();

    if ( ! $user || ! $user->ID ) {
        $error_response = [
            'code' => 'rest_not_logged_in',
            'message' => 'You are not currently logged in.',
            'data' => [ 'status' => 401 ]
        ];

        // Log the unsuccessful authentication attempt
        error_log( "❌ SAP Cart API: No authenticated user found" );
        sap_cart_log_api( "❌ SAP-Cart AUTH FAILED [ID: {$request_id}]: " . wp_json_encode( $error_response, JSON_PRETTY_PRINT ) );
        sap_cart_log_api( "🚫 SAP-Cart Authentication Error: No authenticated user found" );

        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }

    error_log( "🔍 SAP Cart API: Authenticated user: {$user->ID} ({$user->user_login})" );
    sap_cart_log_api( "🔐 SAP-Cart Authenticated User [ID: {$request_id}]: {$user->ID} ({$user->user_login})" );

    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP Cart API: User has capability: {$cap}" );
            sap_cart_log_api( "✅ SAP-Cart AUTH SUCCESS [ID: {$request_id}]: User {$user->ID} has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];

    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP Cart API: User has allowed role: {$role}" );
            sap_cart_log_api( "✅ SAP-Cart AUTH SUCCESS [ID: {$request_id}]: User {$user->ID} has allowed role: {$role}" );
            return true;
        }
    }

    // User is authenticated but lacks sufficient permissions
    $error_response = [
        'code' => 'rest_forbidden',
        'message' => 'Sorry, you are not allowed to access this endpoint.',
        'data' => [ 'status' => 403 ]
    ];

    error_log( "❌ SAP Cart API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    sap_cart_log_api( "❌ SAP-Cart AUTH FAILED [ID: {$request_id}]: " . wp_json_encode( $error_response, JSON_PRETTY_PRINT ) );
    sap_cart_log_api( "🚫 SAP-Cart Permission Error: User {$user->ID} ({$user->user_login}) lacks sufficient permissions. User roles: " . implode( ', ', $user_roles ) );

    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}

/**
 * Main SAP Cart API endpoint
 */
function sap_cart_endpoint( WP_REST_Request $request ) {
    $start_time = microtime( true );
    $request_id = uniqid( 'sap_cart_' );

    // Log API call start with comprehensive details
    sap_cart_log_api( "🚀 SAP-Cart API CALL START [ID: {$request_id}]" );
    sap_cart_log_api( "📡 Request Method: " . $request->get_method() );
    sap_cart_log_api( "📡 Request URL: " . $request->get_route() );
    sap_cart_log_api( "📡 Request Time: " . current_time( 'mysql' ) );
    sap_cart_log_api( "📡 User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_cart_log_api( "📡 Client IP: " . sap_cart_get_client_ip() );

    // Log authentication details
    $current_user = wp_get_current_user();
    if ( $current_user && $current_user->ID ) {
        sap_cart_log_api( "🔐 Authenticated User: {$current_user->ID} ({$current_user->user_login})" );
    }

    $data = $request->get_json_params();

    // Log the incoming data (sanitized for security)
    $sanitized_data = sap_cart_sanitize_log_data( $data );
    sap_cart_log_api( "📥 SAP-Cart Request Data [ID: {$request_id}]: " . wp_json_encode( $sanitized_data, JSON_PRETTY_PRINT ) );

    // Validation
    if ( empty( $data ) || ! is_array( $data ) ) {
        $error_response = [
            'code' => 'invalid_data',
            'message' => 'No valid JSON data received',
            'data' => [ 'status' => 400 ]
        ];

        error_log( "❌ No valid JSON data received" );
        sap_cart_log_api( "❌ SAP-Cart VALIDATION ERROR [ID: {$request_id}]: " . wp_json_encode( $error_response, JSON_PRETTY_PRINT ) );
        sap_cart_log_api( "🚫 SAP-Cart Request failed: No valid JSON data received" );

        return new WP_Error( 'invalid_data', 'No valid JSON data received', [ 'status'=>400 ] );
    }

    // Process the cart data (this is where we'll handle the incoming cart data)
    $response = [
        'success' => true,
        'message' => 'Cart data received successfully',
        'timestamp' => current_time( 'mysql' ),
        'request_id' => $request_id,
        'data_received' => count( $data )
    ];

    // Log API call completion
    $end_time = microtime( true );
    $execution_time = round( ( $end_time - $start_time ) * 1000, 2 ); // Convert to milliseconds

    sap_cart_log_api( "📤 SAP-Cart Response [ID: {$request_id}]: " . wp_json_encode( $response, JSON_PRETTY_PRINT ) );
    sap_cart_log_api( "⏱️ SAP-Cart API CALL COMPLETE [ID: {$request_id}] - Execution Time: {$execution_time}ms" );
    sap_cart_log_api( "✅ SAP-Cart Success: {$response['success']}, Request ID: {$response['request_id']}" );

    return rest_ensure_response( $response );
}

/**
 * Trigger cart data collection when cart page is accessed
 */
function sap_cart_trigger_on_cart_page() {
    error_log( "🛒 SAP Cart: Cart page accessed, triggering data collection" );

    // Only trigger for logged-in users
    if ( ! is_user_logged_in() ) {
        error_log( "🛒 SAP Cart: User not logged in, skipping cart data collection" );
        return;
    }

    // Collect cart data
    $cart_data = sap_cart_collect_data();

    if ( $cart_data ) {
        // Send data to our own endpoint (this will be handled via AJAX)
        sap_cart_log_api( "🛒 SAP Cart: Cart data collected for user " . get_current_user_id() );

        // Add JavaScript to send data via AJAX
        add_action( 'wp_footer', 'sap_cart_add_ajax_script' );
    } else {
        error_log( "🛒 SAP Cart: No cart data to send" );
    }
}

/**
 * Enqueue scripts for cart page
 */
function sap_cart_enqueue_scripts() {
    if ( is_cart() ) {
        wp_enqueue_script( 'jquery' );
    }
}

/**
 * Add AJAX script to send cart data
 */
function sap_cart_add_ajax_script() {
    if ( ! is_cart() ) {
        return;
    }

    $cart_data = sap_cart_collect_data();
    if ( ! $cart_data ) {
        return;
    }

    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        console.log('🛒 SAP Cart: Sending cart data to endpoint');

        var cartData = <?php echo wp_json_encode( $cart_data ); ?>;

        $.ajax({
            url: '<?php echo rest_url( 'wc/v3/sap-cart' ); ?>',
            type: 'POST',
            data: JSON.stringify(cartData),
            contentType: 'application/json',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', '<?php echo wp_create_nonce( 'wp_rest' ); ?>');
            },
            success: function(response) {
                console.log('🛒 SAP Cart: Data sent successfully', response);
            },
            error: function(xhr, status, error) {
                console.error('🛒 SAP Cart: Error sending data', error);
                console.error('Response:', xhr.responseText);
            }
        });
    });
    </script>
    <?php
}

/**
 * Collect cart and user data
 */
function sap_cart_collect_data() {
    if ( ! function_exists( 'WC' ) || ! WC()->cart ) {
        return false;
    }

    $current_user = wp_get_current_user();
    if ( ! $current_user || ! $current_user->ID ) {
        return false;
    }

    // Get cart items
    $cart_items = [];
    foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
        $product = $cart_item['data'];
        $cart_items[] = [
            'product_id' => $cart_item['product_id'],
            'quantity' => $cart_item['quantity'],
        ];
    }

    // Get SAP customer data from database
    $sap_customer_data = sap_cart_get_customer_data( $current_user->ID );

    // Get user metadata
    $user_meta = get_user_meta( $current_user->ID );

    // Build the JSON structure
    $order_data = [
        'order' => [
            'id' => 0, // No order ID since this is cart data
            'date_created' => current_time( 'c' ), // ISO 8601 format
            'status' => 'cart', // Custom status for cart
            'currency' => get_woocommerce_currency(),
            'total' => WC()->cart->get_total( 'edit' ),
            'purchase_order' => $user_meta['purchase_order'][0] ?? '',
            'Cust_Req_Ship_Date' => $user_meta['Cust_Req_Ship_Date'][0] ?? '',
            'freight_terms' => $user_meta['freight_terms'][0] ?? '',
            'shipping_conditions' => $user_meta['shipping_conditions'][0] ?? '',
            'shipping_account' => $user_meta['shipping_account'][0] ?? '',
            'end_user' => $user_meta['end_user'][0] ?? '',
            'end_use' => $user_meta['end_use'][0] ?? '',
            'billing' => sap_cart_get_billing_data( $current_user, $sap_customer_data, $user_meta ),
            'shipping' => sap_cart_get_shipping_data( $current_user, $user_meta ),
            'line_items' => $cart_items,
        ]
    ];

    return $order_data;
}

/**
 * Write to custom API log file
 */
function sap_cart_log_api( $message ) {
    $log_dir = WP_CONTENT_DIR . '/logs';
    $log_file = $log_dir . '/APIlogs-Cart.log';

    // Create logs directory if it doesn't exist
    if ( ! file_exists( $log_dir ) ) {
        wp_mkdir_p( $log_dir );
    }

    // Format log entry with timestamp
    $timestamp = current_time( 'Y-m-d H:i:s' );
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;

    // Write to log file
    file_put_contents( $log_file, $log_entry, FILE_APPEND | LOCK_EX );

    // Also log to WordPress error log for backup (optional)
    error_log( $message );
}

/**
 * Get client IP address for logging
 */
function sap_cart_get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

    foreach ( $ip_keys as $key ) {
        if ( ! empty( $_SERVER[$key] ) ) {
            $ip = sanitize_text_field( $_SERVER[$key] );
            // Handle comma-separated IPs (from proxies)
            if ( strpos( $ip, ',' ) !== false ) {
                $ip = trim( explode( ',', $ip )[0] );
            }
            if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                return $ip;
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
}

/**
 * Sanitize data for logging (remove sensitive information)
 */
function sap_cart_sanitize_log_data( $data ) {
    if ( ! is_array( $data ) ) {
        return $data;
    }

    $sanitized = $data;

    // Remove or mask sensitive fields
    $sensitive_fields = ['password', 'token', 'secret', 'key'];

    array_walk_recursive( $sanitized, function( &$value, $key ) use ( $sensitive_fields ) {
        if ( in_array( strtolower( $key ), $sensitive_fields ) ) {
            $value = '[REDACTED]';
        }
        // Mask email addresses partially
        if ( strtolower( $key ) === 'email' && is_string( $value ) && strpos( $value, '@' ) !== false ) {
            $parts = explode( '@', $value );
            if ( count( $parts ) === 2 ) {
                $username = $parts[0];
                $domain = $parts[1];
                $masked_username = substr( $username, 0, 2 ) . str_repeat( '*', max( 0, strlen( $username ) - 2 ) );
                $value = $masked_username . '@' . $domain;
            }
        }
    });

    return $sanitized;
}

/**
 * Get SAP customer data from database
 */
function sap_cart_get_customer_data( $user_id ) {
    global $wpdb;

    // Get customer ID from user meta
    $customer_id = get_user_meta( $user_id, '_customer', true );

    if ( empty( $customer_id ) ) {
        return null;
    }

    $table_name = $wpdb->prefix . 'sap_soldto_customers';

    // Check if table exists
    if ( $wpdb->get_var( "SHOW TABLES LIKE '{$table_name}'" ) != $table_name ) {
        return null;
    }

    $sap_customer = $wpdb->get_row( $wpdb->prepare(
        "SELECT customer_id, company_code, country_code, price_group, Z7_Partner_no, company, address_line1, address_line2, city, postcode, country_region, state_county FROM {$table_name} WHERE customer_id = %s",
        $customer_id
    ) );

    return $sap_customer;
}

/**
 * Get billing data
 */
function sap_cart_get_billing_data( $user, $sap_customer_data, $user_meta ) {
    return [
        'billing_id' => $sap_customer_data->customer_id ?? '',
        'Z7_Partner_no' => $sap_customer_data->Z7_Partner_no ?? '',
        'first_name' => $user_meta['billing_first_name'][0] ?? $user_meta['first_name'][0] ?? '',
        'last_name' => $user_meta['billing_last_name'][0] ?? $user_meta['last_name'][0] ?? '',
        'company_code' => $sap_customer_data->company_code ?? '',
        'company_name' => $sap_customer_data->company ?? $user_meta['billing_company'][0] ?? '',
        'address_1' => $sap_customer_data->address_line1 ?? $user_meta['billing_address_1'][0] ?? '',
        'address_2' => $sap_customer_data->address_line2 ?? $user_meta['billing_address_2'][0] ?? '',
        'city' => $sap_customer_data->city ?? $user_meta['billing_city'][0] ?? '',
        'state' => $sap_customer_data->state_county ?? $user_meta['billing_state'][0] ?? '',
        'postcode' => $sap_customer_data->postcode ?? $user_meta['billing_postcode'][0] ?? '',
        'country' => $sap_customer_data->country_region ?? $user_meta['billing_country'][0] ?? '',
        'email' => $user->user_email,
        'phone' => $user_meta['billing_phone'][0] ?? '',
    ];
}

/**
 * Get shipping data
 */
function sap_cart_get_shipping_data( $user, $user_meta ) {
    return [
        'shipping_id' => $user_meta['_customer'][0] ?? '',
        'first_name' => $user_meta['shipping_first_name'][0] ?? $user_meta['first_name'][0] ?? '',
        'last_name' => $user_meta['shipping_last_name'][0] ?? $user_meta['last_name'][0] ?? '',
        'company_name' => $user_meta['shipping_company'][0] ?? '',
        'address_1' => $user_meta['shipping_address_1'][0] ?? '',
        'address_2' => $user_meta['shipping_address_2'][0] ?? '',
        'city' => $user_meta['shipping_city'][0] ?? '',
        'state' => $user_meta['shipping_state'][0] ?? '',
        'postcode' => $user_meta['shipping_postcode'][0] ?? '',
        'country' => $user_meta['shipping_country'][0] ?? '',
    ];
}
