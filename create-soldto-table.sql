-- SQL to create SAP SoldTo Customers table
-- This table stores customer information from SAP SoldTo API

CREATE TABLE wp_sap_soldto_customers (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    customer_id varchar(50) NOT NULL,
    
    -- SAP SoldTo fields
    company_code varchar(50) DEFAULT NULL,
    country_code varchar(10) DEFAULT NULL,
    price_group varchar(50) DEFAULT NULL,
    email varchar(255) DEFAULT NULL,
    Z7_Partner_no varchar(50) DEFAULT NULL,
    
    -- Billing address fields
    company varchar(255) DEFAULT NULL,
    address_line1 varchar(255) DEFAULT NULL,
    address_line2 varchar(255) DEFAULT NULL,
    city varchar(100) DEFAULT NULL,
    postcode varchar(20) DEFAULT NULL,
    country_region varchar(10) DEFAULT NULL,
    state_county varchar(100) DEFAULT NULL,
    
    -- ShipTo addresses (stored as JSON array)
    shiptos longtext DEFAULT NULL,
    
    -- WordPress integration
    wp_user_id bigint(20) DEFAULT NULL,
    
    -- Timestamps
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    PRIMARY KEY (id),
    UNIQUE KEY customer_id (customer_id),
    KEY wp_user_id (wp_user_id),
    KEY company_code (company_code),
    KEY country_code (country_code),
    KEY price_group (price_group),
    KEY Z7_Partner_no (Z7_Partner_no),
    KEY email (email),
    KEY created_at (created_at),
    KEY updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Comments for documentation
ALTER TABLE wp_sap_soldto_customers
COMMENT = 'SAP SoldTo customer data with billing addresses and metadata';

-- Sample data structure comment
/*
This table stores customer information from SAP SoldTo API including:

Fields:
- customer_id: SAP customer identifier (unique)
- company_code: SAP company code
- country_code: SAP country code
- price_group: SAP price group
- email: Customer email address
- Z7_Partner_no: SAP Z7 Partner number (new field)
- company: Billing company name
- address_line1: Billing address line 1
- address_line2: Billing address line 2
- city: Billing city
- postcode: Billing postal code
- country_region: Billing country/region
- state_county: Billing state/county
- shiptos: JSON array of shipping location IDs
- wp_user_id: Linked WordPress user ID (if exists)
- created_at: Record creation timestamp
- updated_at: Record last update timestamp

Benefits:
- Stores complete SAP customer data
- Links to WordPress users when available
- Supports multiple shipping locations per customer
- Maintains audit trail with timestamps
- Optimized with proper indexes for fast queries
*/
