<?php
/**
 * Plugin Name: SAP Pricing API
 * Description: Processes SAP pricing data and updates WooCommerce product prices
 * Version: 1.0
 * Author: ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Hook to create/update database tables on plugin activation
register_activation_hook( __FILE__, 'sap_pricing_create_tables' );

/**
 * Create or update pricing tables with valid_to column
 */
function sap_pricing_create_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // Define the three pricing tables
    $tables = [
        $wpdb->prefix . 'price_list',
        $wpdb->prefix . 'price_list_eur',
        $wpdb->prefix . 'price_list_gbp'
    ];

    foreach ( $tables as $table_name ) {
        $sql = "CREATE TABLE $table_name (
            id int(10) unsigned NOT NULL AUTO_INCREMENT,
            product_sku varchar(255) NOT NULL,
            price decimal(10,3) NOT NULL,
            price_code_id int(10) unsigned NOT NULL,
            valid_to varchar(10) DEFAULT NULL,
            PRIMARY KEY (id),
            KEY product_sku (product_sku)
        ) $charset_collate;";

        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );

        // Check if valid_to column exists, if not add it (for existing tables)
        $column_exists = $wpdb->get_results( $wpdb->prepare(
            "SHOW COLUMNS FROM $table_name LIKE %s",
            'valid_to'
        ) );

        if ( empty( $column_exists ) ) {
            $wpdb->query( "ALTER TABLE $table_name ADD COLUMN valid_to varchar(10) DEFAULT NULL" );
            error_log( "✅ Added valid_to column to $table_name" );
        }
    }

    error_log( "✅ SAP Pricing tables created/updated successfully" );
}

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-pricing', [
        'methods'             => 'POST',
        'callback'            => 'sap_process_pricing',
        'permission_callback' => 'sap_pricing_permission_check',
    ] );
    
    // Test endpoint
    register_rest_route( 'wc/v3', '/sap-pricing-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP Pricing API is active',
                'timestamp' => current_time( 'mysql' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true',
    ] );

    // Debug endpoint to inspect product pricing meta
    register_rest_route( 'wc/v3', '/sap-pricing-debug', [
        'methods'             => 'POST',
        'callback'            => function( WP_REST_Request $request ) {
            $data = $request->get_json_params();
            if ( empty( $data['product_id'] ) ) {
                return new WP_Error( 'missing_product_id', 'product_id is required', [ 'status' => 400 ] );
            }

            $product_id = (int) $data['product_id'];
            $pricing_data = get_product_pricing_data( $product_id );

            // Get all meta to see what's actually stored
            $all_meta = get_post_meta( $product_id );
            $filtered_meta = [];
            foreach ( $all_meta as $key => $value ) {
                $filtered_meta[$key] = is_array( $value ) && count( $value ) === 1 ? $value[0] : $value;
            }

            return rest_ensure_response([
                'product_id' => $product_id,
                'current_pricing_data' => $pricing_data,
                'all_meta' => $filtered_meta,
                'product_title' => get_the_title( $product_id ),
                'product_type' => get_post_type( $product_id )
            ]);
        },
        'permission_callback' => '__return_true',
    ] );
} );

/**
 * Permission callback for SAP Pricing API
 * Uses WordPress REST API authentication (Basic Auth or Application Passwords)
 */
function sap_pricing_permission_check( WP_REST_Request $request ) {
    error_log( "🔍 SAP Pricing API: Checking WordPress REST API authentication" );
    
    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();
    
    if ( ! $user || ! $user->ID ) {
        error_log( "❌ SAP Pricing API: No authenticated user found" );
        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }
    
    error_log( "🔍 SAP Pricing API: Authenticated user: {$user->ID} ({$user->user_login})" );
    
    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
        'edit_products',        // Product management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP Pricing API: User has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];
    
    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP Pricing API: User has allowed role: {$role}" );
            return true;
        }
    }

    error_log( "❌ SAP Pricing API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}

/**
 * Main endpoint to process SAP pricing data
 */
function sap_process_pricing( WP_REST_Request $request ) {
    $start_time = microtime( true );
    $request_id = uniqid( 'sap_pricing_' );

    // Log API call start with comprehensive details
    sap_pricing_log_api( "🚀 SAP-Pricing API CALL START [ID: {$request_id}]" );
    sap_pricing_log_api( "📡 Request Method: " . $request->get_method() );
    sap_pricing_log_api( "📡 Request URL: " . $request->get_route() );
    sap_pricing_log_api( "📡 Request Time: " . current_time( 'mysql' ) );
    sap_pricing_log_api( "📡 User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_pricing_log_api( "📡 Client IP: " . sap_pricing_get_client_ip() );

    // Log authentication details
    $current_user = wp_get_current_user();
    if ( $current_user && $current_user->ID ) {
        sap_pricing_log_api( "🔐 Authenticated User: {$current_user->ID} ({$current_user->user_login})" );
    }

    $data = $request->get_json_params();

    // Log the incoming data (sanitized for security)
    $sanitized_data = sap_pricing_sanitize_log_data( $data );
    sap_pricing_log_api( "📥 SAP-Pricing Request Data [ID: {$request_id}]: " . wp_json_encode( $sanitized_data, JSON_PRETTY_PRINT ) );

    // Validation
    if ( empty( $data ) || ! is_array( $data ) ) {
        error_log( "❌ Invalid data structure" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected JSON object.', [ 'status' => 400 ] );
    }

    // Check for pricing array
    if ( ! isset( $data['pricing'] ) || ! is_array( $data['pricing'] ) ) {
        error_log( "❌ Invalid data structure - missing pricing array" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected "pricing" array.', [ 'status' => 400 ] );
    }

    $pricing_items = $data['pricing'];
    sap_pricing_log_api( "📦 SAP-Pricing Processing batch of " . count( $pricing_items ) . " materials [ID: {$request_id}]" );

    // Process each pricing item
    $processed_materials = [];
    $total_errors = [];
    $total_updated_currencies = 0;

    foreach ( $pricing_items as $index => $pricing_item ) {
        $material_result = process_single_pricing_item( $pricing_item, $index, $request_id );

        if ( is_wp_error( $material_result ) ) {
            $total_errors[] = "Item {$index}: " . $material_result->get_error_message();
            error_log( "❌ Error processing pricing item {$index}: " . $material_result->get_error_message() );
        } else {
            $processed_materials[] = $material_result;
            $total_updated_currencies += $material_result['total_currencies_updated'];
            error_log( "✅ Successfully processed material: {$material_result['material_number']}" );
        }
    }

    // Build response
    $response = [
        'success' => true,
        'total_materials_processed' => count( $processed_materials ),
        'total_materials_requested' => count( $pricing_items ),
        'total_currencies_updated' => $total_updated_currencies,
        'processed_materials' => $processed_materials,
        'errors' => $total_errors
    ];

    if ( ! empty( $total_errors ) ) {
        $response['partial_success'] = true;
        $response['success'] = count( $processed_materials ) > 0; // Success if at least one material was processed
    }

    // Log API call completion
    $end_time = microtime( true );
    $execution_time = round( ( $end_time - $start_time ) * 1000, 2 ); // Convert to milliseconds

    sap_pricing_log_api( "📤 SAP-Pricing Response [ID: {$request_id}]: " . wp_json_encode( $response, JSON_PRETTY_PRINT ) );
    sap_pricing_log_api( "⏱️ SAP-Pricing API CALL COMPLETE [ID: {$request_id}] - Execution Time: {$execution_time}ms" );
    sap_pricing_log_api( "✅ SAP-Pricing Batch Success: {$response['success']}, Materials Processed: {$response['total_materials_processed']}/{$response['total_materials_requested']}, Total Currencies Updated: {$response['total_currencies_updated']}" );

    return rest_ensure_response( $response );
}

/**
 * Process a single pricing item
 */
function process_single_pricing_item( $pricing_item, $index, $request_id ) {
    // Validate required fields for this pricing item
    if ( empty( $pricing_item['materialNumber'] ) ) {
        error_log( "❌ Missing materialNumber for item {$index}" );
        return new WP_Error( 'missing_material_number', "materialNumber is required for item {$index}", [ 'status' => 400 ] );
    }

    if ( empty( $pricing_item['price'] ) || ! is_array( $pricing_item['price'] ) ) {
        error_log( "❌ Missing or invalid price data for item {$index}" );
        return new WP_Error( 'missing_price_data', "price data is required and must be an object for item {$index}", [ 'status' => 400 ] );
    }

    $material_number = sanitize_text_field( $pricing_item['materialNumber'] );

    // Trim leading zeros from material number for consistency
    $original_material_number = $material_number;
    $material_number = ltrim( $material_number, '0' );
    if ( empty( $material_number ) ) {
        $material_number = '0';
    }

    // Log the transformation if it occurred
    if ( $original_material_number !== $material_number ) {
        error_log( "🔄 SAP-Pricing: Material number trimmed: '{$original_material_number}' -> '{$material_number}'" );
        sap_pricing_log_api( "🔄 SAP-Pricing Material number trimmed: '{$original_material_number}' -> '{$material_number}' [ID: {$request_id}, Item: {$index}]" );
    }

    sap_pricing_log_api( "🔍 SAP-Pricing Processing material {$material_number} [ID: {$request_id}, Item: {$index}]" );

    // Find product by material number (SKU)
    $product_id = find_product_by_sku( $material_number );

    if ( ! $product_id ) {
        error_log( "❌ Product not found for material number: {$material_number}" );
        sap_pricing_log_api( "❌ SAP-Pricing Product not found: {$material_number} [ID: {$request_id}, Item: {$index}]" );
        return new WP_Error( 'product_not_found', "Product not found for materialNumber: {$material_number}", [ 'status' => 404 ] );
    }

    error_log( "🔍 Found product ID {$product_id} for material number {$material_number}" );
    sap_pricing_log_api( "✅ SAP-Pricing Found product ID {$product_id} for material {$material_number} [ID: {$request_id}, Item: {$index}]" );

    // Process pricing data for each currency
    $updated_currencies = [];
    $errors = [];

    foreach ( $pricing_item['price'] as $currency => $price_data ) {
        $result = update_product_price_for_currency( $product_id, $currency, $price_data );

        if ( is_wp_error( $result ) ) {
            $errors[] = "Currency {$currency}: " . $result->get_error_message();
            error_log( "❌ Error updating price for currency {$currency}: " . $result->get_error_message() );
            sap_pricing_log_api( "❌ SAP-Pricing Currency update failed: {$material_number} - {$currency} - " . $result->get_error_message() . " [ID: {$request_id}, Item: {$index}]" );
        } else {
            $updated_currencies[] = $result;
            error_log( "✅ Successfully updated price for currency {$currency}" );
            sap_pricing_log_api( "✅ SAP-Pricing Currency updated: {$material_number} - {$currency} - Price: {$result['price']} [ID: {$request_id}, Item: {$index}]" );
        }
    }

    // Return result for this material
    $material_result = [
        'product_id' => $product_id,
        'material_number' => $material_number,
        'updated_currencies' => $updated_currencies,
        'total_currencies_updated' => count( $updated_currencies ),
        'errors' => $errors
    ];

    if ( ! empty( $errors ) ) {
        $material_result['partial_success'] = true;
    }

    return $material_result;
}

/**
 * Find WooCommerce product by SKU
 */
function find_product_by_sku( $sku ) {
    // Search for product by SKU
    $product_id = wc_get_product_id_by_sku( $sku );

    if ( $product_id ) {
        return $product_id;
    }

    // Fallback: search using WP_Query
    $query = new WP_Query([
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => 1,
        'fields' => 'ids',
        'meta_query' => [
            [
                'key' => '_sku',
                'value' => $sku,
                'compare' => '='
            ]
        ]
    ]);

    if ( $query->have_posts() ) {
        return $query->posts[0];
    }

    return false;
}

/**
 * Update product price for a specific currency in custom price tables
 */
function update_product_price_for_currency( $product_id, $currency, $price_data ) {
    global $wpdb;

    // Validate price data
    if ( ! isset( $price_data['price'] ) ) {
        return new WP_Error( 'missing_price', 'price is required for price data' );
    }

    $price = (float) $price_data['price'];
    $price_code = isset( $price_data['price_code'] ) ? sanitize_text_field( $price_data['price_code'] ) : '';
    $valid_to = isset( $price_data['valid_to'] ) ? sanitize_text_field( $price_data['valid_to'] ) : null;

    // Validate valid_to date format if provided (DD/MM/YYYY)
    if ( ! empty( $valid_to ) ) {
        if ( ! preg_match( '/^\d{2}\/\d{2}\/\d{4}$/', $valid_to ) ) {
            return new WP_Error( 'invalid_date_format', 'valid_to must be in DD/MM/YYYY format' );
        }

        // Additional validation: check if it's a valid date
        $date_parts = explode( '/', $valid_to );
        if ( ! checkdate( (int) $date_parts[1], (int) $date_parts[0], (int) $date_parts[2] ) ) {
            return new WP_Error( 'invalid_date', 'valid_to contains an invalid date' );
        }
    }

    // Get product SKU
    $product_sku = get_post_meta( $product_id, '_sku', true );
    if ( empty( $product_sku ) ) {
        error_log( "❌ Product {$product_id} has no SKU" );
        return new WP_Error( 'missing_sku', "Product {$product_id} has no SKU" );
    }

    // Map currency to table name
    $currency_upper = strtoupper( $currency );
    $table_name = '';

    switch ( $currency_upper ) {
        case 'US':
        case 'USD':
            $table_name = $wpdb->prefix . 'price_list';
            break;
        case 'EU':
        case 'EUR':
            $table_name = $wpdb->prefix . 'price_list_eur';
            break;
        case 'GB':
        case 'GBP':
            $table_name = $wpdb->prefix . 'price_list_gbp';
            break;
        default:
            return new WP_Error( 'unsupported_currency', "Unsupported currency: {$currency}" );
    }

    error_log( "🔍 Updating price table {$table_name} for SKU {$product_sku} - Currency: {$currency}, Price: {$price}, Price Code: {$price_code}, Valid To: " . ( $valid_to ?: 'NULL' ) );

    // Get price_code_id (assuming it's just the price_code string for now, or you might need a lookup table)
    $price_code_id = ! empty( $price_code ) ? $price_code : 0;

    // Check if record exists
    $existing_record = $wpdb->get_row( $wpdb->prepare(
        "SELECT id FROM {$table_name} WHERE product_sku = %s",
        $product_sku
    ) );

    if ( $existing_record ) {
        // Update existing record
        $update_data = [
            'price' => $price,
            'price_code_id' => $price_code_id,
            'valid_to' => $valid_to
        ];

        $update_format = [
            '%f', // price as float
            '%s', // price_code_id as string
            '%s'  // valid_to as string (can be NULL)
        ];

        $result = $wpdb->update(
            $table_name,
            $update_data,
            [
                'product_sku' => $product_sku
            ],
            $update_format,
            [
                '%s'  // product_sku as string
            ]
        );

        if ( $result === false ) {
            error_log( "❌ Failed to update price in {$table_name} for SKU {$product_sku}: " . $wpdb->last_error );
            return new WP_Error( 'price_update_failed', "Failed to update price for currency {$currency}: " . $wpdb->last_error );
        }

        error_log( "✅ Updated existing record in {$table_name} for SKU {$product_sku}" );
        $operation = 'updated';
        $record_id = $existing_record->id;

    } else {
        // Insert new record
        $insert_data = [
            'product_sku' => $product_sku,
            'price' => $price,
            'price_code_id' => $price_code_id,
            'valid_to' => $valid_to
        ];

        $insert_format = [
            '%s', // product_sku as string
            '%f', // price as float
            '%s', // price_code_id as string
            '%s'  // valid_to as string (can be NULL)
        ];

        $result = $wpdb->insert(
            $table_name,
            $insert_data,
            $insert_format
        );

        if ( $result === false ) {
            error_log( "❌ Failed to insert price in {$table_name} for SKU {$product_sku}: " . $wpdb->last_error );
            return new WP_Error( 'price_insert_failed', "Failed to insert price for currency {$currency}: " . $wpdb->last_error );
        }

        error_log( "✅ Inserted new record in {$table_name} for SKU {$product_sku}" );
        $operation = 'inserted';
        $record_id = $wpdb->insert_id;
    }

    // Verify the update by reading back the data
    $final_record = $wpdb->get_row( $wpdb->prepare(
        "SELECT * FROM {$table_name} WHERE product_sku = %s",
        $product_sku
    ) );

    if ( ! $final_record ) {
        error_log( "⚠️ Could not verify price update for SKU {$product_sku} in {$table_name}" );
    } else {
        error_log( "🔍 Final verification - SKU: {$final_record->product_sku}, Price: {$final_record->price}, Price Code ID: {$final_record->price_code_id}, Valid To: " . ( $final_record->valid_to ?: 'NULL' ) );
    }

    error_log( "✅ {$operation} product price for SKU {$product_sku} in currency {$currency}: {$price}, price code: {$price_code}, valid to: " . ( $valid_to ?: 'NULL' ) );

    return [
        'currency' => $currency,
        'price' => $price,
        'price_code' => $price_code,
        'valid_to' => $valid_to,
        'table_name' => $table_name,
        'product_sku' => $product_sku,
        'operation' => $operation,
        'record_id' => $record_id,
        'final_record' => $final_record ? [
            'id' => $final_record->id,
            'product_sku' => $final_record->product_sku,
            'price' => $final_record->price,
            'price_code_id' => $final_record->price_code_id,
            'valid_to' => $final_record->valid_to
        ] : null
    ];
}

/**
 * Helper function to get current pricing data for a product from custom tables (for debugging)
 */
function get_product_pricing_data( $product_id ) {
    global $wpdb;

    $pricing_data = [];

    // Get product SKU
    $product_sku = get_post_meta( $product_id, '_sku', true );
    if ( empty( $product_sku ) ) {
        return [ 'error' => 'Product has no SKU' ];
    }

    // Define the price tables
    $price_tables = [
        'USD' => $wpdb->prefix . 'price_list',
        'EUR' => $wpdb->prefix . 'price_list_eur',
        'GBP' => $wpdb->prefix . 'price_list_gbp'
    ];

    foreach ( $price_tables as $currency => $table_name ) {
        $record = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE product_sku = %s",
            $product_sku
        ) );

        if ( $record ) {
            $pricing_data[$currency] = [
                'id' => $record->id,
                'product_sku' => $record->product_sku,
                'price' => $record->price,
                'price_code_id' => $record->price_code_id,
                'valid_to' => $record->valid_to,
                'table_name' => $table_name
            ];
        }
    }

    $pricing_data['product_sku'] = $product_sku;
    return $pricing_data;
}

/**
 * Write to custom API log file
 */
function sap_pricing_log_api( $message ) {
    $log_dir = WP_CONTENT_DIR . '/logs';
    $log_file = $log_dir . '/APIlogs.log';

    // Create logs directory if it doesn't exist
    if ( ! file_exists( $log_dir ) ) {
        wp_mkdir_p( $log_dir );
    }

    // Format log entry with timestamp
    $timestamp = current_time( 'Y-m-d H:i:s' );
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;

    // Write to log file
    file_put_contents( $log_file, $log_entry, FILE_APPEND | LOCK_EX );

    // Also log to WordPress error log for backup (optional)
    error_log( $message );
}

/**
 * Get client IP address for logging
 */
function sap_pricing_get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

    foreach ( $ip_keys as $key ) {
        if ( ! empty( $_SERVER[$key] ) ) {
            $ip = sanitize_text_field( $_SERVER[$key] );
            // Handle comma-separated IPs (from proxies)
            if ( strpos( $ip, ',' ) !== false ) {
                $ip = trim( explode( ',', $ip )[0] );
            }
            if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                return $ip;
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
}

/**
 * Sanitize data for logging (remove sensitive information)
 */
function sap_pricing_sanitize_log_data( $data ) {
    if ( ! is_array( $data ) ) {
        return $data;
    }

    $sanitized = $data;

    // Remove or mask sensitive fields
    $sensitive_fields = ['password', 'token', 'secret', 'key'];

    array_walk_recursive( $sanitized, function( &$value, $key ) use ( $sensitive_fields ) {
        if ( in_array( strtolower( $key ), $sensitive_fields ) ) {
            $value = '[REDACTED]';
        }
    });

    return $sanitized;
}
