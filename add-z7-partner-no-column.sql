-- SQL to add Z7_Partner_no column to existing wp_sap_soldto_customers table
-- Run this script if you already have the table and need to add the new field

-- Add the Z7_Partner_no column after the email column
ALTER TABLE wp_sap_soldto_customers 
ADD COLUMN Z7_Partner_no varchar(50) DEFAULT NULL 
AFTER email;

-- Add index for the new column for better query performance
ALTER TABLE wp_sap_soldto_customers 
ADD KEY Z7_Partner_no (Z7_Partner_no);

-- Verify the column was added successfully
-- You can run this query to check the table structure:
-- DESCRIBE wp_sap_soldto_customers;

-- Sample query to test the new column
-- SELECT customer_id, Z7_Partner_no FROM wp_sap_soldto_customers LIMIT 5;
