<?php
/**
 * Test script to verify Ship To addresses display functionality
 * This can be run as a WordPress page or standalone script to test the implementation
 */

// If running in WordPress
if (function_exists('get_users')) {
    echo "<h1>Ship To Addresses Display Test</h1>";
    
    // Test 1: Check for users with WCMCA addresses
    echo "<h2>Test 1: Users with WCMCA Addresses</h2>";
    
    $users = get_users([
        'meta_key' => '_wcmca_additional_addresses',
        'number' => 10
    ]);
    
    if (empty($users)) {
        echo "<p>❌ No users found with WCMCA addresses</p>";
        echo "<p>💡 Make sure the SAP ShipTo integration has been run to populate address data.</p>";
    } else {
        echo "<p>✅ Found " . count($users) . " users with WCMCA addresses</p>";
        
        foreach ($users as $user) {
            echo "<h3>User: {$user->display_name} (ID: {$user->ID})</h3>";
            
            $addresses = get_user_meta($user->ID, '_wcmca_additional_addresses', true);
            
            if (!is_array($addresses)) {
                echo "<p>❌ No valid address data found</p>";
                continue;
            }
            
            // Filter for shipping addresses
            $shipping_addresses = array();
            foreach ($addresses as $address_id => $address) {
                if (isset($address['type']) && $address['type'] === 'shipping') {
                    $shipping_addresses[$address_id] = $address;
                }
            }
            
            if (empty($shipping_addresses)) {
                echo "<p>⚠️ No shipping addresses found for this user</p>";
            } else {
                echo "<p>✅ Found " . count($shipping_addresses) . " shipping addresses:</p>";
                
                foreach ($shipping_addresses as $address_id => $address) {
                    $display_address_id = isset($address['address_id']) ? $address['address_id'] : $address_id;
                    $address_name = $address['address_internal_name'] ?? 'Unnamed Address';
                    
                    echo "<div style='margin-left: 20px; padding: 10px; border: 1px solid #ddd; margin-bottom: 10px;'>";
                    echo "<strong>Address: {$address_name}</strong><br>";
                    echo "<strong>Address ID:</strong> <code>{$display_address_id}</code><br>";
                    
                    // Display key address fields
                    $key_fields = [
                        'shipping_company' => 'Company',
                        'shipping_address_1' => 'Address Line 1',
                        'shipping_city' => 'City',
                        'shipping_country' => 'Country',
                        'shipping_postcode' => 'Postal Code'
                    ];
                    
                    foreach ($key_fields as $field_key => $field_label) {
                        if (isset($address[$field_key]) && !empty($address[$field_key])) {
                            $field_value = $address[$field_key];
                            
                            // Convert country code to name if possible
                            if ($field_key === 'shipping_country' && function_exists('WC')) {
                                $countries = WC()->countries->get_countries();
                                $field_value = isset($countries[$field_value]) ? $countries[$field_value] : $field_value;
                            }
                            
                            echo "<strong>{$field_label}:</strong> {$field_value}<br>";
                        }
                    }
                    
                    if (isset($address['shipping_is_default_address']) && $address['shipping_is_default_address']) {
                        echo "<span style='background: #0073aa; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;'>Default</span><br>";
                    }
                    
                    echo "</div>";
                }
            }
            
            echo "<hr>";
        }
    }
    
    // Test 2: Check if our display function would work
    echo "<h2>Test 2: Display Function Compatibility</h2>";
    
    if (class_exists('WC_Admin_Profile')) {
        echo "<p>✅ WC_Admin_Profile class exists</p>";
        
        if (method_exists('WC_Admin_Profile', 'display_ship_to_addresses')) {
            echo "<p>✅ display_ship_to_addresses method exists</p>";
        } else {
            echo "<p>❌ display_ship_to_addresses method not found - implementation may not be loaded</p>";
        }
    } else {
        echo "<p>❌ WC_Admin_Profile class not found</p>";
    }
    
    // Test 3: Check WordPress hooks
    echo "<h2>Test 3: WordPress Hooks</h2>";
    
    global $wp_filter;
    
    $hooks_to_check = ['show_user_profile', 'edit_user_profile'];
    
    foreach ($hooks_to_check as $hook) {
        if (isset($wp_filter[$hook])) {
            echo "<p>✅ Hook '{$hook}' has " . count($wp_filter[$hook]->callbacks) . " priority levels</p>";
            
            foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        is_object($callback['function'][0]) && 
                        get_class($callback['function'][0]) === 'WC_Admin_Profile' &&
                        $callback['function'][1] === 'display_ship_to_addresses') {
                        echo "<p>✅ Found our display_ship_to_addresses callback on '{$hook}' hook</p>";
                    }
                }
            }
        } else {
            echo "<p>⚠️ Hook '{$hook}' not found</p>";
        }
    }
    
    echo "<h2>Summary</h2>";
    echo "<p>If all tests pass, the Ship To addresses should now be displayed on user edit pages in the WordPress admin.</p>";
    echo "<p>To view: Go to Users → All Users → Edit any user that has shipping addresses.</p>";
    
} else {
    echo "<h1>Error</h1>";
    echo "<p>This script must be run within WordPress context.</p>";
}
?>
