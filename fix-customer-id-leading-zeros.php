<?php
<?php
// Load WordPress if not already loaded
if (!defined('ABSPATH')) {
    // Adjust this path to your WordPress installation
    require_once('/wp-config.php');
    require_once('/wp-blog-header.php');
}

// Rest of your migration script code...
/**
 * Migration script to fix customer IDs with leading zeros in user metadata
 * This script finds and fixes _customer metadata fields that have leading zeros
 */

// Only run this in WordPress environment

echo "<h2>🔧 Fixing Customer ID Leading Zeros in User Metadata</h2>";

// Check if user has permission to run this
if (!current_user_can('manage_options')) {
    echo "<p style='color: red;'>❌ You don't have permission to run this migration script.</p>";
    exit;
}

// Step 1: Find all users with _customer metadata that has leading zeros
echo "<h3>Step 1: Finding Users with Leading Zero Customer IDs</h3>";

global $wpdb;

// Query to find all users with _customer metadata that starts with zeros
$users_with_leading_zeros = $wpdb->get_results("
    SELECT um.user_id, um.meta_value as customer_id, u.user_login, u.user_email
    FROM {$wpdb->usermeta} um
    JOIN {$wpdb->users} u ON um.user_id = u.ID
    WHERE um.meta_key = '_customer' 
    AND um.meta_value REGEXP '^0+[0-9]'
    AND um.meta_value != '0'
    ORDER BY um.user_id
");

if (empty($users_with_leading_zeros)) {
    echo "<p style='color: green;'>✅ No users found with leading zero customer IDs!</p>";
    echo "<p>All customer IDs are already properly formatted.</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Found " . count($users_with_leading_zeros) . " users with leading zero customer IDs:</p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>User ID</th><th>Username</th><th>Email</th><th>Current Customer ID</th><th>Trimmed Customer ID</th></tr>";
    
    foreach ($users_with_leading_zeros as $user_data) {
        $original_customer_id = $user_data->customer_id;
        $trimmed_customer_id = ltrim($original_customer_id, '0');
        
        // If becomes empty after trimming, keep one zero
        if (empty($trimmed_customer_id)) {
            $trimmed_customer_id = '0';
        }
        
        echo "<tr>";
        echo "<td>{$user_data->user_id}</td>";
        echo "<td>{$user_data->user_login}</td>";
        echo "<td>{$user_data->user_email}</td>";
        echo "<td><code style='background: #ffe6e6;'>{$original_customer_id}</code></td>";
        echo "<td><code style='background: #e6ffe6;'>{$trimmed_customer_id}</code></td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Step 2: Show what will be updated (dry run)
if (!empty($users_with_leading_zeros)) {
    echo "<h3>Step 2: Migration Preview</h3>";
    
    $dry_run = !isset($_POST['confirm_migration']);
    
    if ($dry_run) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
        echo "<h4>⚠️ DRY RUN MODE</h4>";
        echo "<p>This is a preview of what will be changed. No actual changes will be made yet.</p>";
        echo "</div>";
    }
    
    echo "<p><strong>Changes to be made:</strong></p>";
    
    $updates_needed = [];
    foreach ($users_with_leading_zeros as $user_data) {
        $original_customer_id = $user_data->customer_id;
        $trimmed_customer_id = ltrim($original_customer_id, '0');
        
        if (empty($trimmed_customer_id)) {
            $trimmed_customer_id = '0';
        }
        
        if ($original_customer_id !== $trimmed_customer_id) {
            $updates_needed[] = [
                'user_id' => $user_data->user_id,
                'user_login' => $user_data->user_login,
                'original' => $original_customer_id,
                'trimmed' => $trimmed_customer_id
            ];
        }
    }
    
    echo "<ul>";
    foreach ($updates_needed as $update) {
        echo "<li>User <strong>{$update['user_login']}</strong> (ID: {$update['user_id']}): ";
        echo "<code>{$update['original']}</code> → <code>{$update['trimmed']}</code></li>";
    }
    echo "</ul>";
    
    // Step 3: Execute migration or show confirmation form
    if ($dry_run) {
        echo "<h3>Step 3: Execute Migration</h3>";
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
        echo "<h4>⚠️ WARNING</h4>";
        echo "<p>This will permanently modify user metadata in your database.</p>";
        echo "<p>Make sure you have a database backup before proceeding!</p>";
        echo "</div>";
        
        echo "<form method='post'>";
        echo "<p><label><input type='checkbox' name='backup_confirmed' required> I confirm that I have a database backup</label></p>";
        echo "<p><label><input type='checkbox' name='understand_changes' required> I understand that this will modify " . count($updates_needed) . " user records</label></p>";
        echo "<input type='hidden' name='confirm_migration' value='1'>";
        echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Execute Migration</button>";
        echo "</form>";
        
    } else {
        // Execute the actual migration
        echo "<h3>Step 3: Executing Migration</h3>";
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        foreach ($updates_needed as $update) {
            $result = update_user_meta($update['user_id'], '_customer', $update['trimmed']);
            
            if ($result !== false) {
                $success_count++;
                echo "<p style='color: green;'>✅ Updated user {$update['user_login']}: {$update['original']} → {$update['trimmed']}</p>";
                
                // Log the change
                error_log("🔄 Migration: Updated user {$update['user_id']} ({$update['user_login']}) customer ID: {$update['original']} → {$update['trimmed']}");
            } else {
                $error_count++;
                $error_msg = "Failed to update user {$update['user_login']} (ID: {$update['user_id']})";
                $errors[] = $error_msg;
                echo "<p style='color: red;'>❌ {$error_msg}</p>";
                error_log("❌ Migration error: {$error_msg}");
            }
        }
        
        echo "<h4>Migration Results:</h4>";
        echo "<ul>";
        echo "<li style='color: green;'>✅ Successfully updated: {$success_count} users</li>";
        if ($error_count > 0) {
            echo "<li style='color: red;'>❌ Errors: {$error_count} users</li>";
        }
        echo "</ul>";
        
        if ($error_count > 0) {
            echo "<h4>Errors:</h4>";
            echo "<ul>";
            foreach ($errors as $error) {
                echo "<li style='color: red;'>{$error}</li>";
            }
            echo "</ul>";
        }
        
        // Verify the changes
        echo "<h4>Verification:</h4>";
        $remaining_issues = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->usermeta}
            WHERE meta_key = '_customer' 
            AND meta_value REGEXP '^0+[0-9]'
            AND meta_value != '0'
        ");
        
        if ($remaining_issues == 0) {
            echo "<p style='color: green;'>✅ Verification successful! No more leading zero customer IDs found.</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ {$remaining_issues} customer IDs still have leading zeros. You may need to run this script again.</p>";
        }
    }
}

echo "<h3>🎉 Migration Script Complete!</h3>";

if (!empty($users_with_leading_zeros) && !$dry_run) {
    echo "<p><strong>What was done:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Fixed customer ID storage in user metadata</li>";
    echo "<li>✅ Removed leading zeros from _customer fields</li>";
    echo "<li>✅ Updated SoldTo API to prevent future leading zero storage</li>";
    echo "<li>✅ Maintained consistency with database storage</li>";
    echo "</ul>";
}

echo "<p><strong>Going forward:</strong></p>";
echo "<ul>";
echo "<li>✅ New SoldTo API calls will automatically trim leading zeros before storing</li>";
echo "<li>✅ User metadata will be consistent with database storage</li>";
echo "<li>✅ Cart API and other integrations will work correctly</li>";
echo "</ul>";

echo "<p><strong>Related Files Updated:</strong></p>";
echo "<ul>";
echo "<li><code>sap-soldto.php</code> - Added zero-trimming to user metadata storage</li>";
echo "<li><code>sap-inventory.php</code> - Already had zero-trimming for product search</li>";
echo "</ul>";
?>
