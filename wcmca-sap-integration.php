<?php
/**
 * Plugin Name: WCMCA SAP Integration
 * Plugin URI: https://yoursite.com
 * Description: Makes WooCommerce Multiple Customer Addresses plugin read from SAP table when WordPress metadata is empty. Solves the issue where SAP addresses exist but no WordPress user was created yet.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 * Requires at least: 5.0
 * Tested up to: 6.3
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WCMCA_SAP_VERSION', '1.0.0');
define('WCMCA_SAP_PLUGIN_FILE', __FILE__);
define('WCMCA_SAP_PLUGIN_DIR', plugin_dir_path(__FILE__));

/**
 * Check if required plugins are active
 */
function wcmca_sap_check_dependencies() {
    $missing_plugins = array();

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        $missing_plugins[] = 'WooCommerce';
    }

    // Check if WCMCA is active
    if (!class_exists('WCMCA_Customer')) {
        $missing_plugins[] = 'WooCommerce Multiple Customer Addresses';
    }

    // Check if SAP ShipTo plugin functions exist
    if (!function_exists('sap_shipto_get_database_addresses')) {
        $missing_plugins[] = 'SAP ShipTo Addresses Plugin';
    }

    if (!empty($missing_plugins)) {
        add_action('admin_notices', function() use ($missing_plugins) {
            echo '<div class="notice notice-error"><p>';
            echo '<strong>WCMCA SAP Integration:</strong> The following required plugins are missing or inactive: ';
            echo implode(', ', $missing_plugins);
            echo '</p></div>';
        });
        return false;
    }

    return true;
}

/**
 * Plugin activation hook
 */
function wcmca_sap_activate() {
    // Check dependencies on activation
    if (!wcmca_sap_check_dependencies()) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die('WCMCA SAP Integration requires WooCommerce, WooCommerce Multiple Customer Addresses, and SAP ShipTo Addresses plugins to be active.');
    }

    // Log activation
    error_log('✅ WCMCA SAP Integration plugin activated');
}
register_activation_hook(__FILE__, 'wcmca_sap_activate');

/**
 * Plugin deactivation hook
 */
function wcmca_sap_deactivate() {
    error_log('❌ WCMCA SAP Integration plugin deactivated');
}
register_deactivation_hook(__FILE__, 'wcmca_sap_deactivate');

/**
 * Enhanced get_addresses function that falls back to SAP table
 */
function wcmca_sap_get_addresses($user_id) {
    global $wcmca_customer_model;
    
    // First, try the original WCMCA method (WordPress metadata)
    $wp_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
    
    if (!empty($wp_addresses) && is_array($wp_addresses)) {
        // Found addresses in WordPress metadata, use them
        error_log("🔍 WCMCA-SAP: Found " . count($wp_addresses) . " addresses in WordPress metadata for user {$user_id}");
        return $wp_addresses;
    }
    
    // No addresses in WordPress metadata, check SAP table
    $customer_id = get_user_meta($user_id, '_customer', true);
    
    if (empty($customer_id)) {
        // User has no SAP customer ID, return empty array
        error_log("🔍 WCMCA-SAP: No customer_id found for user {$user_id}, returning empty addresses");
        return array();
    }
    
    // Get addresses from SAP table
    $sap_data = sap_shipto_get_database_addresses($customer_id);
    
    if ($sap_data && isset($sap_data['addresses']) && is_array($sap_data['addresses'])) {
        error_log("🔍 WCMCA-SAP: Found " . count($sap_data['addresses']) . " addresses in SAP table for customer {$customer_id} (user {$user_id})");
        
        // Optional: Sync SAP addresses to WordPress metadata for future use
        update_user_meta($user_id, '_wcmca_additional_addresses', $sap_data['addresses']);
        error_log("✅ WCMCA-SAP: Synced SAP addresses to WordPress metadata for user {$user_id}");
        
        return $sap_data['addresses'];
    }
    
    error_log("🔍 WCMCA-SAP: No addresses found in SAP table for customer {$customer_id} (user {$user_id})");
    return array();
}

/**
 * Enhanced add_addresses function that syncs to SAP table
 */
function wcmca_sap_add_addresses($user_id, $new_address) {
    global $wcmca_customer_model;
    
    // First, use the original WCMCA method
    $address_id = $wcmca_customer_model->add_addresses($user_id, $new_address);
    
    if ($address_id) {
        // Successfully added to WordPress, now sync to SAP table
        $customer_id = get_user_meta($user_id, '_customer', true);
        
        if (!empty($customer_id)) {
            // Get all addresses and sync to SAP table
            $all_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
            
            if (is_array($all_addresses)) {
                wcmca_sap_sync_to_database($customer_id, $all_addresses, $user_id);
            }
        }
    }
    
    return $address_id;
}

/**
 * Enhanced update_addresses function that syncs to SAP table
 */
function wcmca_sap_update_addresses($user_id, $address_id, $new_address) {
    global $wcmca_customer_model;
    
    // First, use the original WCMCA method
    $wcmca_customer_model->update_addresses($user_id, $address_id, $new_address);
    
    // Now sync to SAP table
    $customer_id = get_user_meta($user_id, '_customer', true);
    
    if (!empty($customer_id)) {
        // Get all addresses and sync to SAP table
        $all_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
        
        if (is_array($all_addresses)) {
            wcmca_sap_sync_to_database($customer_id, $all_addresses, $user_id);
        }
    }
}

/**
 * Enhanced delete_addresses function that syncs to SAP table
 */
function wcmca_sap_delete_addresses($user_id, $address_ids) {
    global $wcmca_customer_model;
    
    // First, use the original WCMCA method
    $wcmca_customer_model->delete_addresses($user_id, $address_ids);
    
    // Now sync to SAP table
    $customer_id = get_user_meta($user_id, '_customer', true);
    
    if (!empty($customer_id)) {
        // Get remaining addresses and sync to SAP table
        $all_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
        
        if (is_array($all_addresses)) {
            wcmca_sap_sync_to_database($customer_id, $all_addresses, $user_id);
        } else {
            // No addresses left, clear SAP table
            wcmca_sap_sync_to_database($customer_id, array(), $user_id);
        }
    }
}

/**
 * Sync WordPress addresses to SAP database table
 */
function wcmca_sap_sync_to_database($customer_id, $addresses, $user_id = null) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'sap_shipto_addresses';
    
    // Prepare serialized data
    $serialized_addresses = maybe_serialize($addresses);
    $total_addresses = is_array($addresses) ? count($addresses) : 0;
    
    // Check for default address
    $has_default = false;
    if (is_array($addresses)) {
        foreach ($addresses as $address) {
            if (isset($address['shipping_is_default_address']) || isset($address['billing_is_default_address'])) {
                $has_default = true;
                break;
            }
        }
    }
    
    // Prepare database record
    $db_record = [
        'customer_id' => $customer_id,
        'wcmca_addresses_data' => $serialized_addresses,
        'total_addresses' => $total_addresses,
        'has_default_address' => $has_default ? 1 : 0,
        'status' => 'active',
        'updated_at' => current_time('mysql')
    ];
    
    if ($user_id) {
        $db_record['wp_user_id'] = $user_id;
    }
    
    // Check if record exists
    $existing_record = $wpdb->get_row($wpdb->prepare(
        "SELECT id FROM {$table_name} WHERE customer_id = %s",
        $customer_id
    ));
    
    if ($existing_record) {
        // Update existing record
        $result = $wpdb->update(
            $table_name,
            $db_record,
            ['customer_id' => $customer_id],
            ['%s', '%s', '%d', '%d', '%s', '%s', '%d'],
            ['%s']
        );
        
        error_log("🔄 WCMCA-SAP: Updated SAP table for customer {$customer_id} with {$total_addresses} addresses");
    } else {
        // Insert new record
        $db_record['created_at'] = current_time('mysql');
        
        $result = $wpdb->insert(
            $table_name,
            $db_record,
            ['%s', '%s', '%d', '%d', '%s', '%s', '%s', '%d']
        );
        
        error_log("➕ WCMCA-SAP: Inserted new SAP table record for customer {$customer_id} with {$total_addresses} addresses");
    }
    
    return $result !== false;
}

/**
 * Override WCMCA_Customer class methods
 */
class WCMCA_SAP_Customer_Override {

    public function __construct() {
        // Hook into WordPress to override WCMCA functions
        add_action('plugins_loaded', array($this, 'init_overrides'), 25);
    }

    public function init_overrides() {
        global $wcmca_customer_model;

        if (!$wcmca_customer_model || !class_exists('WCMCA_Customer')) {
            return; // WCMCA not loaded
        }

        // Replace the global WCMCA customer model with our enhanced version
        $wcmca_customer_model = new WCMCA_SAP_Enhanced_Customer();

        error_log("✅ WCMCA-SAP: Enhanced customer model initialized");
    }
}

/**
 * Enhanced WCMCA_Customer class that reads from SAP table
 */
class WCMCA_SAP_Enhanced_Customer extends WCMCA_Customer {

    /**
     * Override get_addresses to check SAP table as fallback
     */
    public function get_addresses($user_id) {
        if (!isset($user_id) || !is_numeric($user_id)) {
            return "";
        }

        // First, try WordPress metadata (original WCMCA behavior)
        $result = get_user_meta($user_id, '_wcmca_additional_addresses', true);

        if ($result && is_array($result)) {
            // Found addresses in WordPress metadata
            $result = $this->sort_by_name($result);
            error_log("🔍 WCMCA-SAP: Found " . count($result) . " addresses in WordPress metadata for user {$user_id}");
            return $result;
        }

        // No addresses in WordPress metadata, check SAP table
        $customer_id = get_user_meta($user_id, '_customer', true);

        if (empty($customer_id)) {
            error_log("🔍 WCMCA-SAP: No customer_id found for user {$user_id}");
            return array();
        }

        // Get addresses from SAP table
        if (function_exists('sap_shipto_get_database_addresses')) {
            $sap_data = sap_shipto_get_database_addresses($customer_id);

            if ($sap_data && isset($sap_data['addresses']) && is_array($sap_data['addresses'])) {
                $result = $this->sort_by_name($sap_data['addresses']);
                error_log("🔍 WCMCA-SAP: Found " . count($result) . " addresses in SAP table for customer {$customer_id} (user {$user_id})");

                // Sync to WordPress metadata for future use
                update_user_meta($user_id, '_wcmca_additional_addresses', $result);
                error_log("✅ WCMCA-SAP: Synced SAP addresses to WordPress metadata for user {$user_id}");

                return $result;
            }
        }

        error_log("🔍 WCMCA-SAP: No addresses found for customer {$customer_id} (user {$user_id})");
        return array();
    }

    /**
     * Override add_addresses to sync to SAP table
     */
    public function add_addresses($user_id, $new_address) {
        // Use parent method first
        $address_id = parent::add_addresses($user_id, $new_address);

        if ($address_id) {
            $this->sync_to_sap_table($user_id);
        }

        return $address_id;
    }

    /**
     * Override update_addresses to sync to SAP table
     */
    public function update_addresses($user_id, $address_id, $new_address) {
        // Use parent method first
        parent::update_addresses($user_id, $address_id, $new_address);

        $this->sync_to_sap_table($user_id);
    }

    /**
     * Override delete_addresses to sync to SAP table
     */
    public function delete_addresses($user_id, $address_ids) {
        // Use parent method first
        parent::delete_addresses($user_id, $address_ids);

        $this->sync_to_sap_table($user_id);
    }

    /**
     * Sync WordPress addresses to SAP table
     */
    private function sync_to_sap_table($user_id) {
        $customer_id = get_user_meta($user_id, '_customer', true);

        if (!empty($customer_id)) {
            $all_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);

            if (function_exists('wcmca_sap_sync_to_database')) {
                wcmca_sap_sync_to_database($customer_id, $all_addresses ?: array(), $user_id);
            }
        }
    }
}

// Initialize the override system
new WCMCA_SAP_Customer_Override();

/**
 * Add admin menu for manual sync
 */
function wcmca_sap_admin_menu() {
    add_submenu_page(
        'woocommerce',
        'WCMCA SAP Sync',
        'WCMCA SAP Sync',
        'manage_woocommerce',
        'wcmca-sap-sync',
        'wcmca_sap_admin_page'
    );
}
add_action('admin_menu', 'wcmca_sap_admin_menu');

/**
 * Admin page for manual sync
 */
function wcmca_sap_admin_page() {
    if (isset($_POST['sync_existing_data']) && wp_verify_nonce($_POST['_wpnonce'], 'wcmca_sap_sync')) {
        $result = wcmca_sap_sync_existing_data();
        echo '<div class="notice notice-success"><p>Sync completed: ' . $result['synced'] . ' synced, ' . $result['errors'] . ' errors, ' . $result['total_records'] . ' total records.</p></div>';
    }

    ?>
    <div class="wrap">
        <h1>WCMCA SAP Integration</h1>

        <div class="card">
            <h2>Manual Sync</h2>
            <p>This will sync existing SAP address data to WordPress users who don't have addresses in their metadata yet.</p>

            <form method="post">
                <?php wp_nonce_field('wcmca_sap_sync'); ?>
                <input type="submit" name="sync_existing_data" class="button button-primary" value="Sync Existing SAP Data to WordPress">
            </form>
        </div>

        <div class="card">
            <h2>How It Works</h2>
            <ul>
                <li><strong>Automatic Fallback:</strong> When WCMCA can't find addresses in WordPress metadata, it automatically checks the SAP table.</li>
                <li><strong>Seamless Integration:</strong> Existing users continue working normally, new SAP customers get their addresses automatically.</li>
                <li><strong>Two-Way Sync:</strong> When users modify addresses through WordPress, changes are synced back to the SAP table.</li>
                <li><strong>No Data Loss:</strong> All existing functionality is preserved, this just adds SAP table support.</li>
            </ul>
        </div>

        <div class="card">
            <h2>Status</h2>
            <?php
            global $wpdb;

            // Count SAP records
            $sap_table = $wpdb->prefix . 'sap_shipto_addresses';
            $sap_count = $wpdb->get_var("SELECT COUNT(*) FROM {$sap_table} WHERE status = 'active'");

            // Count WordPress users with addresses
            $wp_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->usermeta} WHERE meta_key = '_wcmca_additional_addresses' AND meta_value != ''");

            // Count users with customer_id
            $customer_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->usermeta} WHERE meta_key = '_customer' AND meta_value != ''");

            echo "<p><strong>SAP Address Records:</strong> {$sap_count}</p>";
            echo "<p><strong>WordPress Users with Addresses:</strong> {$wp_count}</p>";
            echo "<p><strong>WordPress Users with Customer ID:</strong> {$customer_count}</p>";
            ?>
        </div>
    </div>
    <?php
}

/**
 * Initialize plugin only if dependencies are met
 */
function wcmca_sap_init() {
    if (wcmca_sap_check_dependencies()) {
        error_log('✅ WCMCA SAP Integration: All dependencies met, plugin active');
    }
}
add_action('plugins_loaded', 'wcmca_sap_init');

/**
 * Manual function to sync existing SAP data to WordPress users
 * Call this once to sync all existing SAP addresses to WordPress users
 */
function wcmca_sap_sync_existing_data() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'sap_shipto_addresses';
    
    // Get all SAP address records
    $sap_records = $wpdb->get_results("
        SELECT customer_id, wcmca_addresses_data, wp_user_id 
        FROM {$table_name} 
        WHERE status = 'active'
    ");
    
    $synced_count = 0;
    $error_count = 0;
    
    foreach ($sap_records as $record) {
        // Find WordPress user for this customer
        $user_id = $record->wp_user_id;
        
        if (!$user_id) {
            // Try to find user by customer_id
            $users = get_users([
                'meta_key' => '_customer',
                'meta_value' => $record->customer_id,
                'number' => 1,
                'fields' => 'ID'
            ]);
            
            $user_id = !empty($users) ? $users[0] : false;
        }
        
        if ($user_id) {
            // Check if user already has addresses in WordPress metadata
            $existing_wp_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
            
            if (empty($existing_wp_addresses)) {
                // No WordPress addresses, sync from SAP
                $sap_addresses = maybe_unserialize($record->wcmca_addresses_data);
                
                if (is_array($sap_addresses) && !empty($sap_addresses)) {
                    update_user_meta($user_id, '_wcmca_additional_addresses', $sap_addresses);
                    $synced_count++;
                    error_log("✅ WCMCA-SAP: Synced addresses for customer {$record->customer_id} to user {$user_id}");
                }
            }
        } else {
            $error_count++;
            error_log("❌ WCMCA-SAP: No WordPress user found for customer {$record->customer_id}");
        }
    }
    
    error_log("🎯 WCMCA-SAP: Sync complete - {$synced_count} synced, {$error_count} errors");
    
    return [
        'synced' => $synced_count,
        'errors' => $error_count,
        'total_records' => count($sap_records)
    ];
}

?>
