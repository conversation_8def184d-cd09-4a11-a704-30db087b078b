<?php
/**
 * Plugin Name: SAP Contract Pricing API
 * Description: Processes SAP contract pricing data and updates customer-specific pricing tables
 * Version: 1.0
 * Author: ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Note: Contract pricing tables already exist:
// - wp_contract_pricing (US/USD)
// - wp_contract_pricing_eur (EU/EUR)
// - wp_contract_pricing_gbp (GB/GBP)
// Each with structure: customer_id, product_sku, new_price

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-contract-pricing', [
        'methods'             => 'POST',
        'callback'            => 'sap_process_contract_pricing',
        'permission_callback' => 'sap_contract_pricing_permission_check',
    ] );
    
    // Test endpoint
    register_rest_route( 'wc/v3', '/sap-contract-pricing-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP Contract Pricing API is active',
                'timestamp' => current_time( 'mysql' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true',
    ] );
} );

/**
 * Permission callback for SAP Contract Pricing API
 * Uses WordPress REST API authentication (Basic Auth or Application Passwords)
 */
function sap_contract_pricing_permission_check( WP_REST_Request $request ) {
    error_log( "🔍 SAP Contract Pricing API: Checking WordPress REST API authentication" );
    
    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();
    
    if ( ! $user || ! $user->ID ) {
        error_log( "❌ SAP Contract Pricing API: No authenticated user found" );
        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }
    
    error_log( "🔍 SAP Contract Pricing API: Authenticated user: {$user->ID} ({$user->user_login})" );
    
    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
        'edit_products',        // Product management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP Contract Pricing API: User has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];
    
    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP Contract Pricing API: User has allowed role: {$role}" );
            return true;
        }
    }

    error_log( "❌ SAP Contract Pricing API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}

/**
 * Main endpoint to process SAP contract pricing data
 */
function sap_process_contract_pricing( WP_REST_Request $request ) {
    $start_time = microtime( true );
    $request_id = uniqid( 'sap_contract_pricing_' );

    // Log API call start with comprehensive details
    sap_contract_pricing_log_api( "🚀 SAP-Contract-Pricing API CALL START [ID: {$request_id}]" );
    sap_contract_pricing_log_api( "📡 Request Method: " . $request->get_method() );
    sap_contract_pricing_log_api( "📡 Request URL: " . $request->get_route() );
    sap_contract_pricing_log_api( "📡 Request Time: " . current_time( 'mysql' ) );
    sap_contract_pricing_log_api( "📡 User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_contract_pricing_log_api( "📡 Client IP: " . sap_contract_pricing_get_client_ip() );

    // Log authentication details
    $current_user = wp_get_current_user();
    if ( $current_user && $current_user->ID ) {
        sap_contract_pricing_log_api( "🔐 Authenticated User: {$current_user->ID} ({$current_user->user_login})" );
    }

    $data = $request->get_json_params();

    // Log the incoming data (sanitized for security)
    $sanitized_data = sap_contract_pricing_sanitize_log_data( $data );
    sap_contract_pricing_log_api( "📥 SAP-Contract-Pricing Request Data [ID: {$request_id}]: " . wp_json_encode( $sanitized_data, JSON_PRETTY_PRINT ) );

    // Validation
    if ( empty( $data ) || ! is_array( $data ) ) {
        error_log( "❌ Invalid data structure" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected JSON object.', [ 'status' => 400 ] );
    }

    // Check for contractPricing array
    if ( ! isset( $data['contractPricing'] ) || ! is_array( $data['contractPricing'] ) ) {
        error_log( "❌ Invalid data structure - missing contractPricing array" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected "contractPricing" array.', [ 'status' => 400 ] );
    }

    $pricing_items = $data['contractPricing'];
    sap_contract_pricing_log_api( "📦 SAP-Contract-Pricing Processing batch of " . count( $pricing_items ) . " materials [ID: {$request_id}]" );

    // Process each contract pricing item
    $processed_materials = [];
    $total_errors = [];
    $total_updated_currencies = 0;

    foreach ( $pricing_items as $index => $pricing_item ) {
        $material_result = process_single_contract_pricing_item( $pricing_item, $index, $request_id );

        if ( is_wp_error( $material_result ) ) {
            $total_errors[] = "Item {$index}: " . $material_result->get_error_message();
            error_log( "❌ Error processing contract pricing item {$index}: " . $material_result->get_error_message() );
        } else {
            $processed_materials[] = $material_result;
            $total_updated_currencies += $material_result['total_currencies_updated'];
            error_log( "✅ Successfully processed material: {$material_result['material_number']}" );
        }
    }

    // Prepare response
    $response = [
        'success' => count( $total_errors ) === 0,
        'total_materials_requested' => count( $pricing_items ),
        'total_materials_processed' => count( $processed_materials ),
        'total_currencies_updated' => $total_updated_currencies,
        'processed_materials' => $processed_materials,
        'errors' => $total_errors,
        'request_id' => $request_id,
        'timestamp' => current_time( 'mysql' )
    ];

    // Log API call completion
    $end_time = microtime( true );
    $execution_time = round( ( $end_time - $start_time ) * 1000, 2 ); // Convert to milliseconds

    sap_contract_pricing_log_api( "📤 SAP-Contract-Pricing Response [ID: {$request_id}]: " . wp_json_encode( $response, JSON_PRETTY_PRINT ) );
    sap_contract_pricing_log_api( "⏱️ SAP-Contract-Pricing API CALL COMPLETE [ID: {$request_id}] - Execution Time: {$execution_time}ms" );
    sap_contract_pricing_log_api( "✅ SAP-Contract-Pricing Batch Success: {$response['success']}, Materials Processed: {$response['total_materials_processed']}/{$response['total_materials_requested']}, Total Currencies Updated: {$response['total_currencies_updated']}" );

    return rest_ensure_response( $response );
}

/**
 * Process a single contract pricing item
 */
function process_single_contract_pricing_item( $pricing_item, $index, $request_id ) {
    // Validate required fields for this pricing item
    if ( empty( $pricing_item['materialNumber'] ) ) {
        error_log( "❌ Missing materialNumber for item {$index}" );
        return new WP_Error( 'missing_material_number', "materialNumber is required for item {$index}", [ 'status' => 400 ] );
    }

    if ( empty( $pricing_item['customer_id'] ) ) {
        error_log( "❌ Missing customer_id for item {$index}" );
        return new WP_Error( 'missing_customer_id', "customer_id is required for item {$index}", [ 'status' => 400 ] );
    }

    if ( empty( $pricing_item['price'] ) || ! is_array( $pricing_item['price'] ) ) {
        error_log( "❌ Missing or invalid price data for item {$index}" );
        return new WP_Error( 'missing_price_data', "price data is required and must be an object for item {$index}", [ 'status' => 400 ] );
    }

    $material_number = sanitize_text_field( $pricing_item['materialNumber'] );
    $customer_id = sanitize_text_field( $pricing_item['customer_id'] );

    // Trim leading zeros from material number for consistency
    $original_material_number = $material_number;
    $material_number = ltrim( $material_number, '0' );
    if ( empty( $material_number ) ) {
        $material_number = '0';
    }

    // Trim leading zeros from customer ID for consistency
    $original_customer_id = $customer_id;
    $customer_id = ltrim( $customer_id, '0' );
    if ( empty( $customer_id ) ) {
        $customer_id = '0';
    }

    // Log transformations if they occurred
    if ( $original_material_number !== $material_number ) {
        error_log( "🔄 SAP-Contract-Pricing: Material number trimmed: '{$original_material_number}' -> '{$material_number}'" );
        sap_contract_pricing_log_api( "🔄 SAP-Contract-Pricing Material number trimmed: '{$original_material_number}' -> '{$material_number}' [ID: {$request_id}, Item: {$index}]" );
    }

    if ( $original_customer_id !== $customer_id ) {
        error_log( "🔄 SAP-Contract-Pricing: Customer ID trimmed: '{$original_customer_id}' -> '{$customer_id}'" );
        sap_contract_pricing_log_api( "🔄 SAP-Contract-Pricing Customer ID trimmed: '{$original_customer_id}' -> '{$customer_id}' [ID: {$request_id}, Item: {$index}]" );
    }

    sap_contract_pricing_log_api( "🔍 SAP-Contract-Pricing Processing material {$material_number} for customer {$customer_id} [ID: {$request_id}, Item: {$index}]" );

    // Process pricing data for each currency
    $updated_currencies = [];
    $errors = [];

    foreach ( $pricing_item['price'] as $currency => $price_data ) {
        $result = update_contract_price_for_currency( $material_number, $customer_id, $currency, $price_data );

        if ( is_wp_error( $result ) ) {
            $errors[] = "Currency {$currency}: " . $result->get_error_message();
            error_log( "❌ Error updating contract price for currency {$currency}: " . $result->get_error_message() );
            sap_contract_pricing_log_api( "❌ SAP-Contract-Pricing Currency update failed: {$material_number} - Customer {$customer_id} - {$currency} - " . $result->get_error_message() . " [ID: {$request_id}, Item: {$index}]" );
        } else {
            $updated_currencies[] = $result;
            error_log( "✅ Successfully updated contract price for currency {$currency}" );
            sap_contract_pricing_log_api( "✅ SAP-Contract-Pricing Currency updated: {$material_number} - Customer {$customer_id} - {$currency} - Price: {$result['price']} [ID: {$request_id}, Item: {$index}]" );
        }
    }

    if ( ! empty( $errors ) ) {
        return new WP_Error( 'currency_update_errors', implode( '; ', $errors ) );
    }

    return [
        'material_number' => $material_number,
        'customer_id' => $customer_id,
        'product_sku' => $material_number,
        'total_currencies_updated' => count( $updated_currencies ),
        'updated_currencies' => $updated_currencies
    ];
}

/**
 * Update contract price for a specific currency in existing contract pricing tables
 */
function update_contract_price_for_currency( $product_sku, $customer_id, $currency, $price_data ) {
    global $wpdb;

    // Validate price data
    if ( ! isset( $price_data['contract_price'] ) ) {
        return new WP_Error( 'missing_contract_price', 'contract_price is required for price data' );
    }

    $price = floatval( $price_data['contract_price'] );
    $valid_to = isset( $price_data['valid_to'] ) ? sanitize_text_field( $price_data['valid_to'] ) : null;

    // Validate price
    if ( $price < 0 ) {
        return new WP_Error( 'invalid_price', 'contract_price must be a positive number' );
    }

    // Product SKU is provided directly from materialNumber
    if ( empty( $product_sku ) ) {
        error_log( "❌ Product SKU is empty" );
        return new WP_Error( 'missing_sku', "Product SKU is required" );
    }

    // Map currency to table name
    $currency_upper = strtoupper( $currency );
    $table_name = '';

    switch ( $currency_upper ) {
        case 'US':
        case 'USD':
            $table_name = $wpdb->prefix . 'contract_pricing';
            break;
        case 'EU':
        case 'EUR':
            $table_name = $wpdb->prefix . 'contract_pricing_eur';
            break;
        case 'GB':
        case 'GBP':
            $table_name = $wpdb->prefix . 'contract_pricing_gbp';
            break;
        default:
            return new WP_Error( 'unsupported_currency', "Unsupported currency: {$currency}" );
    }

    error_log( "🔍 Updating contract pricing table {$table_name} for Customer {$customer_id}, SKU {$product_sku} - Currency: {$currency}, Price: {$price}, Valid To: " . ( $valid_to ?: 'NULL' ) );

    // Check if valid_to column exists in the table
    $valid_to_column_exists = $wpdb->get_results( $wpdb->prepare(
        "SHOW COLUMNS FROM {$table_name} LIKE %s",
        'valid_to'
    ) );

    // Check if record exists for this customer and product
    $existing_record = $wpdb->get_row( $wpdb->prepare(
        "SELECT * FROM {$table_name} WHERE customer_id = %d AND product_sku = %s",
        $customer_id,
        $product_sku
    ) );

    if ( $existing_record ) {
        // Update existing record
        $update_data = [ 'new_price' => $price ];
        $update_format = [ '%f' ];

        // Add valid_to if column exists
        if ( ! empty( $valid_to_column_exists ) && $valid_to !== null ) {
            $update_data['valid_to'] = $valid_to;
            $update_format[] = '%s';
        }

        $result = $wpdb->update(
            $table_name,
            $update_data,
            [
                'customer_id' => $customer_id,
                'product_sku' => $product_sku
            ],
            $update_format,
            [
                '%d', // customer_id as integer
                '%s'  // product_sku as string
            ]
        );

        if ( $result === false ) {
            error_log( "❌ Failed to update contract price in {$table_name} for Customer {$customer_id}, SKU {$product_sku}: " . $wpdb->last_error );
            return new WP_Error( 'contract_price_update_failed', "Failed to update contract price for currency {$currency}: " . $wpdb->last_error );
        }

        error_log( "✅ Updated existing record in {$table_name} for Customer {$customer_id}, SKU {$product_sku}" );
        $operation = 'updated';

    } else {
        // Insert new record
        $insert_data = [
            'customer_id' => $customer_id,
            'product_sku' => $product_sku,
            'new_price' => $price
        ];
        $insert_format = [
            '%d', // customer_id as integer
            '%s', // product_sku as string
            '%f'  // new_price as float
        ];

        // Add valid_to if column exists
        if ( ! empty( $valid_to_column_exists ) && $valid_to !== null ) {
            $insert_data['valid_to'] = $valid_to;
            $insert_format[] = '%s';
        }

        $result = $wpdb->insert(
            $table_name,
            $insert_data,
            $insert_format
        );

        if ( $result === false ) {
            error_log( "❌ Failed to insert contract price in {$table_name} for Customer {$customer_id}, SKU {$product_sku}: " . $wpdb->last_error );
            return new WP_Error( 'contract_price_insert_failed', "Failed to insert contract price for currency {$currency}: " . $wpdb->last_error );
        }

        error_log( "✅ Inserted new record in {$table_name} for Customer {$customer_id}, SKU {$product_sku}" );
        $operation = 'inserted';
    }

    // Verify the update by reading back the data
    $final_record = $wpdb->get_row( $wpdb->prepare(
        "SELECT * FROM {$table_name} WHERE customer_id = %d AND product_sku = %s",
        $customer_id,
        $product_sku
    ) );

    if ( ! $final_record ) {
        error_log( "⚠️ Could not verify contract price update for Customer {$customer_id}, SKU {$product_sku} in {$table_name}" );
    } else {
        $valid_to_display = isset( $final_record->valid_to ) ? $final_record->valid_to : 'N/A';
        error_log( "🔍 Final verification - Customer: {$final_record->customer_id}, SKU: {$final_record->product_sku}, Price: {$final_record->new_price}, Valid To: {$valid_to_display}" );
    }

    return [
        'currency' => $currency,
        'price' => $price,
        'valid_to' => $valid_to,
        'operation' => $operation,
        'table_name' => $table_name
    ];
}

/**
 * Write to custom API log file
 */
function sap_contract_pricing_log_api( $message ) {
    $log_dir = WP_CONTENT_DIR . '/logs';
    $log_file = $log_dir . '/APIlogs.log';

    // Create logs directory if it doesn't exist
    if ( ! file_exists( $log_dir ) ) {
        wp_mkdir_p( $log_dir );
    }

    // Format log entry with timestamp
    $timestamp = current_time( 'Y-m-d H:i:s' );
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;

    // Write to log file
    file_put_contents( $log_file, $log_entry, FILE_APPEND | LOCK_EX );

    // Also log to WordPress error log for backup (optional)
    error_log( $message );
}

/**
 * Get client IP address for logging
 */
function sap_contract_pricing_get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

    foreach ( $ip_keys as $key ) {
        if ( ! empty( $_SERVER[$key] ) ) {
            $ip = sanitize_text_field( $_SERVER[$key] );
            // Handle comma-separated IPs (from proxies)
            if ( strpos( $ip, ',' ) !== false ) {
                $ip = trim( explode( ',', $ip )[0] );
            }
            if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                return $ip;
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
}

/**
 * Sanitize data for logging (remove sensitive information)
 */
function sap_contract_pricing_sanitize_log_data( $data ) {
    if ( ! is_array( $data ) ) {
        return $data;
    }

    $sanitized = $data;

    // Remove or mask sensitive fields
    $sensitive_fields = ['password', 'token', 'secret', 'key'];

    array_walk_recursive( $sanitized, function( &$value, $key ) use ( $sensitive_fields ) {
        if ( in_array( strtolower( $key ), $sensitive_fields ) ) {
            $value = '[REDACTED]';
        }
    });

    return $sanitized;
}
