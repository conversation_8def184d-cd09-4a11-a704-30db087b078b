<?php
/**
 * Test script for SAP Inventory Stock Management functionality
 * This script tests that stock management is automatically enabled when SAP updates inventory
 */

// Load WordPress if not already loaded
if (!defined('ABSPATH')) {
    // Try to find WordPress root directory
    $wp_root_paths = [
        __DIR__ . '/wp-config.php',                    // Same directory
        __DIR__ . '/../wp-config.php',                 // Parent directory
        __DIR__ . '/../../wp-config.php',              // Two levels up
        __DIR__ . '/../../../wp-config.php',           // Three levels up
        $_SERVER['DOCUMENT_ROOT'] . '/wp-config.php'   // Document root
    ];
    
    $wp_loaded = false;
    foreach ($wp_root_paths as $wp_config_path) {
        if (file_exists($wp_config_path)) {
            require_once($wp_config_path);
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress configuration file not found. Please place this file in your WordPress directory or adjust the paths.');
    }
}

echo "<h2>🧪 Testing SAP Inventory Stock Management Functionality</h2>";

// Test 1: Check if SAP Inventory functions exist
echo "<h3>Test 1: SAP Inventory Functions Check</h3>";

$inventory_functions = [
    'sap_process_inventory',
    'process_single_inventory_item',
    'find_product_by_material_number',
    'update_product_stock_for_region'
];

$functions_loaded = 0;
foreach ($inventory_functions as $function_name) {
    if (function_exists($function_name)) {
        echo "<p style='color: green;'>✅ Function {$function_name}() exists</p>";
        $functions_loaded++;
    } else {
        echo "<p style='color: red;'>❌ Function {$function_name}() NOT found</p>";
    }
}

if ($functions_loaded === 0) {
    echo "<p style='color: red;'><strong>❌ SAP Inventory plugin is NOT loaded!</strong></p>";
    echo "<p>Please make sure sap-inventory.php is activated</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ SAP Inventory plugin is loaded ({$functions_loaded}/" . count($inventory_functions) . " functions)!</p>";
}

// Test 2: Find a test product
echo "<h3>Test 2: Finding Test Product</h3>";

// Look for products with numeric titles (likely SAP material numbers)
$test_products = get_posts([
    'post_type' => 'product',
    'post_status' => 'publish',
    'numberposts' => 5,
    'fields' => 'ids'
]);

if (empty($test_products)) {
    echo "<p style='color: red;'>❌ No products found for testing</p>";
    exit;
}

$test_product_id = $test_products[0];
$product_title = get_the_title($test_product_id);

echo "<p style='color: green;'>✅ Using test product: <strong>{$product_title}</strong> (ID: {$test_product_id})</p>";

// Test 3: Check current stock management status
echo "<h3>Test 3: Current Stock Management Status</h3>";

$regions = ['US', 'EU', 'GB'];
$current_status = [];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Region</th><th>Stock Quantity</th><th>Stock Management</th><th>Backorders</th></tr>";

foreach ($regions as $region) {
    $region_lower = strtolower($region);
    $stock_qty = get_post_meta($test_product_id, "_stock_{$region_lower}", true);
    $manage_stock = get_post_meta($test_product_id, "_manage_stock_{$region_lower}", true);
    $backorders = get_post_meta($test_product_id, "_backorders_{$region_lower}", true);
    
    $current_status[$region] = [
        'stock' => $stock_qty,
        'manage' => $manage_stock,
        'backorders' => $backorders
    ];
    
    $manage_color = ($manage_stock === 'yes') ? 'green' : 'red';
    $manage_text = ($manage_stock === 'yes') ? '✅ Enabled' : '❌ Disabled';
    
    echo "<tr>";
    echo "<td>{$region}</td>";
    echo "<td>" . ($stock_qty ?: 'Not set') . "</td>";
    echo "<td style='color: {$manage_color};'>{$manage_text}</td>";
    echo "<td>" . ($backorders ?: 'Not set') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Test 4: Simulate SAP inventory update
echo "<h3>Test 4: Simulating SAP Inventory Update</h3>";

if (function_exists('update_product_stock_for_region')) {
    echo "<p><strong>Testing stock management auto-enabling...</strong></p>";
    
    $test_regions = [
        'US' => ['quantity' => 100, 'backorders' => true],
        'EU' => ['quantity' => 50, 'backorders' => false],
        'GB' => ['quantity' => 25, 'backorders' => true]
    ];
    
    foreach ($test_regions as $region => $data) {
        echo "<h4>Testing Region: {$region}</h4>";
        
        // Call the stock update function
        $result = update_product_stock_for_region(
            $test_product_id,
            $region,
            $data['quantity'],
            $data['backorders']
        );
        
        if ($result) {
            echo "<p style='color: green;'>✅ Stock update successful for {$region}</p>";
            echo "<p>Result details:</p>";
            echo "<ul>";
            echo "<li>Stock Quantity: {$result['stock_quantity']}</li>";
            echo "<li>Allow Backorders: " . ($result['allow_backorders'] ? 'Yes' : 'No') . "</li>";
            echo "<li>Stock Management: " . ($result['stock_management_enabled'] ? '✅ Enabled' : '❌ Disabled') . "</li>";
            echo "<li>Stock Meta Key: {$result['stock_meta_key']}</li>";
            echo "<li>Manage Stock Meta Key: {$result['manage_stock_meta_key']}</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ Stock update failed for {$region}</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ update_product_stock_for_region function not available</p>";
}

// Test 5: Verify stock management was enabled
echo "<h3>Test 5: Verification - Stock Management Status After Update</h3>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Region</th><th>Stock Quantity</th><th>Stock Management</th><th>Backorders</th><th>Status</th></tr>";

foreach ($regions as $region) {
    $region_lower = strtolower($region);
    $stock_qty = get_post_meta($test_product_id, "_stock_{$region_lower}", true);
    $manage_stock = get_post_meta($test_product_id, "_manage_stock_{$region_lower}", true);
    $backorders = get_post_meta($test_product_id, "_backorders_{$region_lower}", true);
    
    $was_enabled_before = ($current_status[$region]['manage'] === 'yes');
    $is_enabled_now = ($manage_stock === 'yes');
    
    if (!$was_enabled_before && $is_enabled_now) {
        $status = '🎉 Enabled by SAP';
        $status_color = 'green';
    } elseif ($is_enabled_now) {
        $status = '✅ Already enabled';
        $status_color = 'blue';
    } else {
        $status = '❌ Not enabled';
        $status_color = 'red';
    }
    
    $manage_color = $is_enabled_now ? 'green' : 'red';
    $manage_text = $is_enabled_now ? '✅ Enabled' : '❌ Disabled';
    
    echo "<tr>";
    echo "<td>{$region}</td>";
    echo "<td>" . ($stock_qty ?: 'Not set') . "</td>";
    echo "<td style='color: {$manage_color};'>{$manage_text}</td>";
    echo "<td>" . ($backorders ?: 'Not set') . "</td>";
    echo "<td style='color: {$status_color};'>{$status}</td>";
    echo "</tr>";
}
echo "</table>";

// Test 6: Show all stock-related metadata
echo "<h3>Test 6: Complete Stock Metadata Overview</h3>";

if (function_exists('get_product_stock_levels')) {
    $stock_levels = get_product_stock_levels($test_product_id);
    
    if (!empty($stock_levels)) {
        echo "<p><strong>All stock-related metadata for product {$test_product_id}:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Meta Key</th><th>Value</th><th>Type</th></tr>";
        
        foreach ($stock_levels as $key => $value) {
            $type = '';
            if (strpos($key, '_stock_') === 0) {
                $type = '📦 Stock Quantity';
            } elseif (strpos($key, '_backorders_') === 0) {
                $type = '🔄 Backorders';
            } elseif (strpos($key, '_manage_stock_') === 0) {
                $type = '⚙️ Stock Management';
            }
            
            echo "<tr>";
            echo "<td><code>{$key}</code></td>";
            echo "<td><strong>{$value}</strong></td>";
            echo "<td>{$type}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No stock-related metadata found</p>";
    }
} else {
    echo "<p style='color: red;'>❌ get_product_stock_levels function not available</p>";
}

echo "<h3>🎉 Testing Complete!</h3>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>✅ SAP Inventory API now automatically enables stock management for each region</li>";
echo "<li>✅ When SAP updates stock quantities, the corresponding _manage_stock_* field is set to 'yes'</li>";
echo "<li>✅ This ensures the custom stock management checkboxes are automatically enabled</li>";
echo "<li>✅ Applies to all regions: US (_manage_stock_us), EU (_manage_stock_eu), GB (_manage_stock_gb)</li>";
echo "</ul>";

echo "<p><strong>What was enhanced:</strong></p>";
echo "<ol>";
echo "<li>Added automatic stock management enabling in <code>update_product_stock_for_region()</code> function</li>";
echo "<li>Each region gets its own stock management field: <code>_manage_stock_{region}</code></li>";
echo "<li>Enhanced logging to show stock management status</li>";
echo "<li>Updated helper functions to include stock management fields</li>";
echo "</ol>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Test with real SAP inventory data to verify automatic enabling works</li>";
echo "<li>Check product admin pages to confirm checkboxes are enabled</li>";
echo "<li>Verify stock management works correctly for each region</li>";
echo "</ol>";
?>
