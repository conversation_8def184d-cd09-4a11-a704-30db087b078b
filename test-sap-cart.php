<?php
/**
 * Test script for SAP Cart API functionality
 * This script tests the new SAP Cart API endpoint and data collection
 */

// Only run this in WordPress environment
if (!defined('ABSPATH')) {
    // For testing outside WordPress, you can define ABSPATH
    // define('ABSPATH', '/path/to/your/wordpress/');
    // require_once(ABSPATH . 'wp-config.php');
    die('This script must be run within WordPress environment');
}

echo "<h2>🛒 Testing SAP Cart API Integration</h2>";

// Test 1: Check if WooCommerce is active
echo "<h3>Test 1: WooCommerce Check</h3>";
if (class_exists('WooCommerce')) {
    echo "<p style='color: green;'>✅ WooCommerce is active</p>";
    
    if (WC()->cart) {
        echo "<p style='color: green;'>✅ WooCommerce cart is available</p>";
        echo "<p>Cart items count: " . WC()->cart->get_cart_contents_count() . "</p>";
        echo "<p>Cart total: " . WC()->cart->get_total() . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ WooCommerce cart not initialized</p>";
    }
} else {
    echo "<p style='color: red;'>❌ WooCommerce is not active!</p>";
}

// Test 2: Check if SAP Cart functions exist
echo "<h3>Test 2: SAP Cart Functions Check</h3>";
$functions_to_check = [
    'sap_cart_endpoint',
    'sap_cart_collect_data',
    'sap_cart_get_customer_data',
    'sap_cart_get_billing_data',
    'sap_cart_get_shipping_data'
];

foreach ($functions_to_check as $function_name) {
    if (function_exists($function_name)) {
        echo "<p style='color: green;'>✅ Function {$function_name}() exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Function {$function_name}() not found</p>";
    }
}

// Test 3: Test REST API endpoint registration
echo "<h3>Test 3: REST API Endpoint Check</h3>";
$rest_server = rest_get_server();
$routes = $rest_server->get_routes();

if (isset($routes['/wc/v3/sap-cart'])) {
    echo "<p style='color: green;'>✅ SAP Cart API endpoint is registered</p>";
    echo "<p>Endpoint: <code>/wp-json/wc/v3/sap-cart</code></p>";
} else {
    echo "<p style='color: red;'>❌ SAP Cart API endpoint not found</p>";
}

if (isset($routes['/wc/v3/sap-cart-test'])) {
    echo "<p style='color: green;'>✅ SAP Cart Test API endpoint is registered</p>";
    echo "<p>Test Endpoint: <code>/wp-json/wc/v3/sap-cart-test</code></p>";
} else {
    echo "<p style='color: red;'>❌ SAP Cart Test API endpoint not found</p>";
}

// Test 4: Test data collection (if user is logged in)
echo "<h3>Test 4: Data Collection Test</h3>";
if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    echo "<p style='color: green;'>✅ User is logged in: {$current_user->user_login} (ID: {$current_user->ID})</p>";
    
    // Test customer data retrieval
    if (function_exists('sap_cart_get_customer_data')) {
        $customer_data = sap_cart_get_customer_data($current_user->ID);
        if ($customer_data) {
            echo "<p style='color: green;'>✅ SAP customer data found</p>";
            echo "<div style='background: #f9f9f9; padding: 10px; margin: 10px 0;'>";
            echo "<strong>Customer Data:</strong><br>";
            echo "Customer ID: " . ($customer_data->customer_id ?? 'N/A') . "<br>";
            echo "Company Code: " . ($customer_data->company_code ?? 'N/A') . "<br>";
            echo "Z7_Partner_no: " . ($customer_data->Z7_Partner_no ?? 'N/A') . "<br>";
            echo "Company: " . ($customer_data->company ?? 'N/A') . "<br>";
            echo "</div>";
        } else {
            echo "<p style='color: orange;'>⚠️ No SAP customer data found for this user</p>";
        }
    }
    
    // Test full cart data collection
    if (function_exists('sap_cart_collect_data')) {
        $cart_data = sap_cart_collect_data();
        if ($cart_data) {
            echo "<p style='color: green;'>✅ Cart data collection successful</p>";
            echo "<div style='background: #f0f8ff; padding: 10px; margin: 10px 0; max-height: 300px; overflow-y: auto;'>";
            echo "<strong>Sample Cart Data (JSON):</strong><br>";
            echo "<pre>" . wp_json_encode($cart_data, JSON_PRETTY_PRINT) . "</pre>";
            echo "</div>";
        } else {
            echo "<p style='color: orange;'>⚠️ No cart data collected (cart might be empty or WooCommerce not available)</p>";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ User not logged in - cannot test data collection</p>";
    echo "<p>Please log in to test cart data collection functionality</p>";
}

// Test 5: Test custom fields mapping
echo "<h3>Test 5: Custom Fields Check</h3>";
if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    $user_meta = get_user_meta($current_user->ID);
    
    $custom_fields = [
        'purchase_order',
        'Cust_Req_Ship_Date',
        'freight_terms',
        'shipping_conditions',
        'shipping_account',
        'end_user',
        'end_use'
    ];
    
    echo "<p><strong>Custom Fields Status:</strong></p>";
    foreach ($custom_fields as $field) {
        $value = $user_meta[$field][0] ?? '';
        if (!empty($value)) {
            echo "<p style='color: green;'>✅ {$field}: {$value}</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ {$field}: (empty)</p>";
        }
    }
    
    echo "<p><em>Note: Empty fields will be sent as blank strings in the JSON</em></p>";
} else {
    echo "<p style='color: orange;'>⚠️ User not logged in - cannot check custom fields</p>";
}

// Test 6: JavaScript Integration Test
echo "<h3>Test 6: JavaScript Integration</h3>";
if (is_cart()) {
    echo "<p style='color: green;'>✅ Currently on cart page - JavaScript should be active</p>";
    echo "<p>Check browser console for cart data sending logs</p>";
} else {
    echo "<p style='color: blue;'>ℹ️ Not on cart page - JavaScript will only run on cart page</p>";
    echo "<p>Visit the cart page to see the automatic data sending in action</p>";
}

echo "<h3>🎉 Testing Complete!</h3>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>✅ SAP Cart API endpoint created at <code>/wp-json/wc/v3/sap-cart</code></li>";
echo "<li>✅ Automatic cart page trigger implemented</li>";
echo "<li>✅ Data collection from SAP SoldTo database, user metadata, and cart items</li>";
echo "<li>✅ JSON structure matches your requirements</li>";
echo "<li>✅ Authentication uses same system as SAP SoldTo API</li>";
echo "</ul>";

echo "<p><strong>How it works:</strong></p>";
echo "<ol>";
echo "<li>When a logged-in user visits the cart page, the system automatically collects:</li>";
echo "<ul>";
echo "<li>Current cart items and quantities</li>";
echo "<li>User data from SAP SoldTo database (Z7_Partner_no, company_code, etc.)</li>";
echo "<li>Custom fields from user metadata</li>";
echo "<li>Billing and shipping information</li>";
echo "</ul>";
echo "<li>Data is formatted into the JSON structure you specified</li>";
echo "<li>JavaScript automatically sends the data to the SAP Cart API endpoint</li>";
echo "<li>All activity is logged to <code>wp-content/logs/APIlogs-Cart.log</code></li>";
echo "</ol>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Add any missing custom fields to user profiles</li>";
echo "<li>Test by visiting the cart page while logged in</li>";
echo "<li>Check the API logs for successful data transmission</li>";
echo "<li>Verify the JSON structure matches your requirements</li>";
echo "</ol>";
?>
