<?php
/**
 * Test script for SAP Inventory zero-trimming functionality
 * This script tests the leading zero removal from materialNumber
 */

// Only run this in WordPress environment
if (!defined('ABSPATH')) {
    // For testing outside WordPress, you can define ABSPATH
    // define('ABSPATH', '/path/to/your/wordpress/');
    // require_once(ABSPATH . 'wp-config.php');
    die('This script must be run within WordPress environment');
}

echo "<h2>🧪 Testing SAP Inventory Zero-Trimming Functionality</h2>";

// Test 1: Check if SAP Inventory functions exist
echo "<h3>Test 1: SAP Inventory Functions Check</h3>";

$inventory_functions = [
    'sap_process_inventory',
    'process_single_inventory_item',
    'find_product_by_material_number',
    'update_product_stock_for_region'
];

$functions_loaded = 0;
foreach ($inventory_functions as $function_name) {
    if (function_exists($function_name)) {
        echo "<p style='color: green;'>✅ Function {$function_name}() exists</p>";
        $functions_loaded++;
    } else {
        echo "<p style='color: red;'>❌ Function {$function_name}() NOT found</p>";
    }
}

if ($functions_loaded === 0) {
    echo "<p style='color: red;'><strong>❌ SAP Inventory plugin is NOT loaded!</strong></p>";
    echo "<p>Please make sure sap-inventory.php is activated</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ SAP Inventory plugin is loaded ({$functions_loaded}/" . count($inventory_functions) . " functions)!</p>";
}

// Test 2: Test zero-trimming logic
echo "<h3>Test 2: Zero-Trimming Logic Test</h3>";

$test_cases = [
    '000000000000000518' => '518',
    '000000000000001234' => '1234',
    '000000000000000001' => '1',
    '000000000000000000' => '0',
    '123456789012345678' => '123456789012345678',
    '000000000000000ABC' => 'ABC',
    '000000000000000000000000000000000000000000000000000000000000000001' => '1'
];

echo "<p><strong>Testing zero-trimming transformation:</strong></p>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Original materialNumber</th><th>Expected Result</th><th>Actual Result</th><th>Status</th></tr>";

foreach ($test_cases as $original => $expected) {
    // Simulate the trimming logic from the updated code
    $trimmed = ltrim($original, '0');
    if (empty($trimmed)) {
        $trimmed = '0';
    }
    
    $status = ($trimmed === $expected) ? '✅ Pass' : '❌ Fail';
    $status_color = ($trimmed === $expected) ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td><code>{$original}</code></td>";
    echo "<td><code>{$expected}</code></td>";
    echo "<td><code>{$trimmed}</code></td>";
    echo "<td style='color: {$status_color};'>{$status}</td>";
    echo "</tr>";
}
echo "</table>";

// Test 3: Test with sample inventory data
echo "<h3>Test 3: Sample Inventory Data Processing Test</h3>";

$sample_inventory_data = [
    'inventories' => [
        [
            'materialNumber' => '000000000000000518',
            'stock' => [
                'US' => [
                    'quantity' => 100,
                    'allowBackorders' => true
                ],
                'EU' => [
                    'quantity' => 50,
                    'allowBackorders' => false
                ]
            ]
        ],
        [
            'materialNumber' => '000000000000001234',
            'stock' => [
                'US' => [
                    'quantity' => 25,
                    'allowBackorders' => false
                ]
            ]
        ]
    ]
];

echo "<p><strong>Sample data structure:</strong></p>";
echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 4px;'>";
echo wp_json_encode($sample_inventory_data, JSON_PRETTY_PRINT);
echo "</pre>";

echo "<p><strong>Expected transformations:</strong></p>";
echo "<ul>";
foreach ($sample_inventory_data['inventories'] as $index => $inventory) {
    $original = $inventory['materialNumber'];
    $trimmed = ltrim($original, '0');
    if (empty($trimmed)) {
        $trimmed = '0';
    }
    echo "<li>Item {$index}: <code>{$original}</code> → <code>{$trimmed}</code></li>";
}
echo "</ul>";

// Test 4: Check if products exist with trimmed material numbers
echo "<h3>Test 4: Product Search Test</h3>";

if (function_exists('find_product_by_material_number')) {
    $test_material_numbers = ['518', '1234', '999999'];
    
    echo "<p><strong>Testing product search with trimmed material numbers:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Material Number</th><th>Product Found</th><th>Product ID</th><th>Product Title</th></tr>";
    
    foreach ($test_material_numbers as $material_number) {
        $product_id = find_product_by_material_number($material_number);
        
        if ($product_id) {
            $product_title = get_the_title($product_id);
            echo "<tr>";
            echo "<td><code>{$material_number}</code></td>";
            echo "<td style='color: green;'>✅ Found</td>";
            echo "<td>{$product_id}</td>";
            echo "<td>{$product_title}</td>";
            echo "</tr>";
        } else {
            echo "<tr>";
            echo "<td><code>{$material_number}</code></td>";
            echo "<td style='color: red;'>❌ Not Found</td>";
            echo "<td>-</td>";
            echo "<td>-</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    echo "<p><em>Note: Products not found is normal if they don't exist in your database. The important thing is that the search is working with trimmed numbers.</em></p>";
} else {
    echo "<p style='color: red;'>❌ find_product_by_material_number function not available for testing</p>";
}

// Test 5: Show before/after comparison
echo "<h3>Test 5: Before/After Comparison</h3>";

echo "<div style='display: flex; gap: 20px;'>";

echo "<div style='flex: 1; background: #ffe6e6; padding: 15px; border-radius: 4px;'>";
echo "<h4>❌ Before (Without Zero-Trimming)</h4>";
echo "<p>Material numbers with leading zeros would fail to find products:</p>";
echo "<ul>";
echo "<li><code>000000000000000518</code> → Product not found</li>";
echo "<li><code>000000000000001234</code> → Product not found</li>";
echo "</ul>";
echo "<p><strong>Result:</strong> API returns errors like 'Product not found for materialNumber: 000000000000000518'</p>";
echo "</div>";

echo "<div style='flex: 1; background: #e6ffe6; padding: 15px; border-radius: 4px;'>";
echo "<h4>✅ After (With Zero-Trimming)</h4>";
echo "<p>Material numbers are trimmed before product search:</p>";
echo "<ul>";
echo "<li><code>000000000000000518</code> → <code>518</code> → Product found</li>";
echo "<li><code>000000000000001234</code> → <code>1234</code> → Product found</li>";
echo "</ul>";
echo "<p><strong>Result:</strong> API successfully processes inventory updates</p>";
echo "</div>";

echo "</div>";

echo "<h3>🎉 Testing Complete!</h3>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>✅ Zero-trimming logic implemented in SAP Inventory API</li>";
echo "<li>✅ Material numbers with leading zeros are now trimmed before product search</li>";
echo "<li>✅ Enhanced logging shows the transformation process</li>";
echo "<li>✅ Consistent with SAP SoldTo API zero-trimming functionality</li>";
echo "</ul>";

echo "<p><strong>What was changed:</strong></p>";
echo "<ol>";
echo "<li>Added zero-trimming logic in <code>process_single_inventory_item()</code> function</li>";
echo "<li>Added logging to show original → trimmed transformation</li>";
echo "<li>Enhanced <code>find_product_by_material_number()</code> with detailed search logging</li>";
echo "<li>Maintains consistency with SoldTo API implementation</li>";
echo "</ol>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Test the inventory API with your original JSON data that was failing</li>";
echo "<li>Check the API logs to see the zero-trimming in action</li>";
echo "<li>Verify that products are now found successfully</li>";
echo "</ol>";

echo "<p><strong>API Endpoint:</strong> <code>/wp-json/wc/v3/sap-inventory</code></p>";
echo "<p><strong>Log File:</strong> <code>wp-content/logs/APIlogs.log</code></p>";
?>
