-- SQL to create SAP Cart table
-- This table stores cart data submissions from the cart page

CREATE TABLE wp_sap_cart (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    
    -- Request tracking
    request_id varchar(100) NOT NULL,
    
    -- Order basic info
    order_id bigint(20) DEFAULT 0,
    date_created datetime DEFAULT CURRENT_TIMESTAMP,
    order_status varchar(50) DEFAULT 'cart',
    currency varchar(10) DEFAULT 'USD',
    total decimal(10,2) DEFAULT 0.00,
    
    -- Custom order fields
    purchase_order varchar(255) DEFAULT NULL,
    cust_req_ship_date date DEFAULT NULL,
    freight_terms varchar(50) DEFAULT NULL,
    shipping_conditions varchar(50) DEFAULT NULL,
    shipping_account varchar(100) DEFAULT NULL,
    end_user varchar(255) DEFAULT NULL,
    end_use varchar(255) DEFAULT NULL,
    
    -- Billing information
    billing_id varchar(100) DEFAULT NULL,
    billing_z7_partner_no varchar(100) DEFAULT NULL,
    billing_first_name varchar(100) DEFAULT NULL,
    billing_last_name varchar(100) DEFAULT NULL,
    billing_company_code varchar(50) DEFAULT NULL,
    billing_company_name varchar(255) DEFAULT NULL,
    billing_address_1 varchar(255) DEFAULT NULL,
    billing_address_2 varchar(255) DEFAULT NULL,
    billing_city varchar(100) DEFAULT NULL,
    billing_state varchar(100) DEFAULT NULL,
    billing_postcode varchar(20) DEFAULT NULL,
    billing_country varchar(10) DEFAULT NULL,
    billing_email varchar(255) DEFAULT NULL,
    billing_phone varchar(50) DEFAULT NULL,
    
    -- Shipping information
    shipping_id varchar(100) DEFAULT NULL,
    shipping_first_name varchar(100) DEFAULT NULL,
    shipping_last_name varchar(100) DEFAULT NULL,
    shipping_company_name varchar(255) DEFAULT NULL,
    shipping_address_1 varchar(255) DEFAULT NULL,
    shipping_address_2 varchar(255) DEFAULT NULL,
    shipping_city varchar(100) DEFAULT NULL,
    shipping_state varchar(100) DEFAULT NULL,
    shipping_postcode varchar(20) DEFAULT NULL,
    shipping_country varchar(10) DEFAULT NULL,
    
    -- Line items (stored as JSON for flexibility)
    line_items longtext DEFAULT NULL,
    line_items_count int(11) DEFAULT 0,
    
    -- WordPress user info
    wp_user_id bigint(20) DEFAULT NULL,
    wp_user_login varchar(60) DEFAULT NULL,
    
    -- Processing status
    processing_status varchar(50) DEFAULT 'received',
    processing_notes text DEFAULT NULL,
    processed_at datetime DEFAULT NULL,
    
    -- Raw data backup
    raw_json_data longtext DEFAULT NULL,
    
    -- Timestamps
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    PRIMARY KEY (id),
    UNIQUE KEY request_id (request_id),
    KEY order_id (order_id),
    KEY order_status (order_status),
    KEY billing_id (billing_id),
    KEY billing_z7_partner_no (billing_z7_partner_no),
    KEY billing_company_code (billing_company_code),
    KEY shipping_id (shipping_id),
    KEY wp_user_id (wp_user_id),
    KEY processing_status (processing_status),
    KEY date_created (date_created),
    KEY created_at (created_at),
    KEY updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Comments for documentation
ALTER TABLE wp_sap_cart
COMMENT = 'SAP Cart submissions with complete order, billing, shipping, and line item data';

-- Sample data structure comment
/*
This table stores complete cart submissions including:

Order Fields:
- order_id: WooCommerce order ID (0 for cart submissions)
- order_status: 'cart', 'pending', 'completed', etc.
- total: Cart/order total amount
- currency: Order currency (USD, EUR, etc.)

Custom Fields:
- purchase_order: Customer purchase order number
- cust_req_ship_date: Customer requested shipping date
- freight_terms: Shipping freight terms
- shipping_conditions: SAP shipping conditions
- shipping_account: Customer shipping account
- end_user: End user information
- end_use: End use description

Billing/Shipping:
- Complete billing and shipping address information
- Z7_Partner_no from SAP integration
- Company codes and customer IDs

Line Items:
- line_items: JSON array of cart items with product_id and quantity
- line_items_count: Number of items for quick queries

Processing:
- processing_status: 'received', 'processing', 'completed', 'failed'
- processing_notes: Any processing messages or errors
- processed_at: When processing was completed

Benefits:
- Complete audit trail of all cart submissions
- Structured data for easy querying and reporting
- JSON backup for complex data structures
- Processing status tracking
- WordPress user integration
*/
