<?php
/**
 * <PERSON><PERSON><PERSON> to create SAP Cart table
 * Run this once to create the wp_sap_cart table
 */

// Only run this in WordPress environment
if (!defined('ABSPATH')) {
    // For testing outside WordPress, you can define ABSPATH
    // define('ABSPATH', '/path/to/your/wordpress/');
    // require_once(ABSPATH . 'wp-config.php');
    die('This script must be run within WordPress environment');
}

echo "<h2>🗄️ Creating SAP Cart Database Table</h2>";

global $wpdb;
$table_name = $wpdb->prefix . 'sap_cart';

// Check if table already exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;

if ($table_exists) {
    echo "<p style='color: orange;'>⚠️ Table {$table_name} already exists!</p>";
    
    // Show current table structure
    $columns = $wpdb->get_results("DESCRIBE {$table_name}");
    echo "<h3>Current Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>" . ($column->Default ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show record count
    $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
    echo "<p><strong>Current records:</strong> {$count}</p>";
    
} else {
    echo "<p style='color: blue;'>ℹ️ Creating table {$table_name}...</p>";
    
    // Create table SQL
    $sql = "CREATE TABLE {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        
        -- Request tracking
        request_id varchar(100) NOT NULL,
        
        -- Order basic info
        order_id bigint(20) DEFAULT 0,
        date_created datetime DEFAULT CURRENT_TIMESTAMP,
        order_status varchar(50) DEFAULT 'cart',
        currency varchar(10) DEFAULT 'USD',
        total decimal(10,2) DEFAULT 0.00,
        
        -- Custom order fields
        purchase_order varchar(255) DEFAULT NULL,
        cust_req_ship_date date DEFAULT NULL,
        freight_terms varchar(50) DEFAULT NULL,
        shipping_conditions varchar(50) DEFAULT NULL,
        shipping_account varchar(100) DEFAULT NULL,
        end_user varchar(255) DEFAULT NULL,
        end_use varchar(255) DEFAULT NULL,
        
        -- Billing information
        billing_id varchar(100) DEFAULT NULL,
        billing_z7_partner_no varchar(100) DEFAULT NULL,
        billing_first_name varchar(100) DEFAULT NULL,
        billing_last_name varchar(100) DEFAULT NULL,
        billing_company_code varchar(50) DEFAULT NULL,
        billing_company_name varchar(255) DEFAULT NULL,
        billing_address_1 varchar(255) DEFAULT NULL,
        billing_address_2 varchar(255) DEFAULT NULL,
        billing_city varchar(100) DEFAULT NULL,
        billing_state varchar(100) DEFAULT NULL,
        billing_postcode varchar(20) DEFAULT NULL,
        billing_country varchar(10) DEFAULT NULL,
        billing_email varchar(255) DEFAULT NULL,
        billing_phone varchar(50) DEFAULT NULL,
        
        -- Shipping information
        shipping_id varchar(100) DEFAULT NULL,
        shipping_first_name varchar(100) DEFAULT NULL,
        shipping_last_name varchar(100) DEFAULT NULL,
        shipping_company_name varchar(255) DEFAULT NULL,
        shipping_address_1 varchar(255) DEFAULT NULL,
        shipping_address_2 varchar(255) DEFAULT NULL,
        shipping_city varchar(100) DEFAULT NULL,
        shipping_state varchar(100) DEFAULT NULL,
        shipping_postcode varchar(20) DEFAULT NULL,
        shipping_country varchar(10) DEFAULT NULL,
        
        -- Line items (stored as JSON for flexibility)
        line_items longtext DEFAULT NULL,
        line_items_count int(11) DEFAULT 0,
        
        -- WordPress user info
        wp_user_id bigint(20) DEFAULT NULL,
        wp_user_login varchar(60) DEFAULT NULL,
        
        -- Processing status
        processing_status varchar(50) DEFAULT 'received',
        processing_notes text DEFAULT NULL,
        processed_at datetime DEFAULT NULL,
        
        -- Raw data backup
        raw_json_data longtext DEFAULT NULL,
        
        -- Timestamps
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- Indexes
        PRIMARY KEY (id),
        UNIQUE KEY request_id (request_id),
        KEY order_id (order_id),
        KEY order_status (order_status),
        KEY billing_id (billing_id),
        KEY billing_z7_partner_no (billing_z7_partner_no),
        KEY billing_company_code (billing_company_code),
        KEY shipping_id (shipping_id),
        KEY wp_user_id (wp_user_id),
        KEY processing_status (processing_status),
        KEY date_created (date_created),
        KEY created_at (created_at),
        KEY updated_at (updated_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // Execute the SQL
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $result = $wpdb->query($sql);
    
    if ($result === false) {
        echo "<p style='color: red;'>❌ Failed to create table!</p>";
        echo "<p><strong>Error:</strong> " . $wpdb->last_error . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Table created successfully!</p>";
        
        // Verify table was created
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
        if ($table_exists) {
            echo "<p style='color: green;'>✅ Table verification successful!</p>";
            
            // Show table structure
            $columns = $wpdb->get_results("DESCRIBE {$table_name}");
            echo "<h3>New Table Structure:</h3>";
            echo "<p><strong>Total columns:</strong> " . count($columns) . "</p>";
            
            echo "<details>";
            echo "<summary>Click to view all columns</summary>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td><strong>{$column->Field}</strong></td>";
                echo "<td>{$column->Type}</td>";
                echo "<td>{$column->Null}</td>";
                echo "<td>" . ($column->Key ? "<span style='color: blue;'>{$column->Key}</span>" : '') . "</td>";
                echo "<td>" . ($column->Default ?? '<em>NULL</em>') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</details>";
        } else {
            echo "<p style='color: red;'>❌ Table creation verification failed!</p>";
        }
    }
}

echo "<h3>🎉 Setup Complete!</h3>";
echo "<p><strong>What's Next:</strong></p>";
echo "<ol>";
echo "<li>✅ SAP Cart table is ready to receive data</li>";
echo "<li>✅ Visit your cart page while logged in to test data collection</li>";
echo "<li>✅ Check the table for new records: <code>SELECT * FROM {$table_name} ORDER BY created_at DESC LIMIT 5;</code></li>";
echo "<li>✅ Monitor the API logs: <code>wp-content/logs/APIlogs-Cart.log</code></li>";
echo "</ol>";

echo "<p><strong>Table Information:</strong></p>";
echo "<ul>";
echo "<li><strong>Table Name:</strong> <code>{$table_name}</code></li>";
echo "<li><strong>Primary Key:</strong> <code>id</code> (auto-increment)</li>";
echo "<li><strong>Unique Key:</strong> <code>request_id</code> (prevents duplicates)</li>";
echo "<li><strong>Key Fields:</strong> billing_id, Z7_Partner_no, company_code, user_id</li>";
echo "<li><strong>JSON Fields:</strong> line_items, raw_json_data</li>";
echo "</ul>";

echo "<p><strong>Sample Query to View Data:</strong></p>";
echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 4px;'>";
echo "SELECT \n";
echo "    id, request_id, order_status, total, currency,\n";
echo "    billing_company_name, billing_z7_partner_no,\n";
echo "    line_items_count, processing_status,\n";
echo "    created_at\n";
echo "FROM {$table_name} \n";
echo "ORDER BY created_at DESC \n";
echo "LIMIT 10;";
echo "</pre>";
?>
