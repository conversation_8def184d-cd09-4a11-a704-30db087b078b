<?php
/**
 * Test script for SAP Cart Database functionality
 * This script tests the database saving functionality
 */

// Only run this in WordPress environment
if (!defined('ABSPATH')) {
    die('This script must be run within WordPress environment');
}

echo "<h2>🧪 Testing SAP Cart Database Integration</h2>";

// Test 1: Check if table exists
echo "<h3>Test 1: Database Table Check</h3>";
global $wpdb;
$table_name = $wpdb->prefix . 'sap_cart';

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
if (!$table_exists) {
    echo "<p style='color: red;'>❌ Table {$table_name} does not exist!</p>";
    echo "<p>Please run <strong>create-sap-cart-table.php</strong> first</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ Table {$table_name} exists</p>";
    
    // Show current record count
    $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
    echo "<p>Current records in table: <strong>{$count}</strong></p>";
}

// Test 2: Test the save function
echo "<h3>Test 2: Database Save Function Test</h3>";

if (function_exists('sap_cart_save_to_database')) {
    echo "<p style='color: green;'>✅ sap_cart_save_to_database() function exists</p>";
    
    // Create test data
    $test_request_id = 'TEST_' . time() . '_' . rand(1000, 9999);
    $test_order_data = [
        'id' => 0,
        'date_created' => current_time('c'),
        'status' => 'cart',
        'currency' => 'USD',
        'total' => '299.99',
        'purchase_order' => 'TEST_PO_' . time(),
        'Cust_Req_Ship_Date' => '2025-08-15',
        'freight_terms' => 'C',
        'shipping_conditions' => '18',
        'shipping_account' => 'TEST_ACCOUNT_123',
        'end_user' => 'Test User',
        'end_use' => 'Testing',
        'billing' => [
            'billing_id' => 'TEST_BILLING_' . time(),
            'Z7_Partner_no' => 'Z7_TEST_' . time(),
            'first_name' => 'John',
            'last_name' => 'Doe',
            'company_code' => '3090',
            'company_name' => 'Test Company Inc',
            'address_1' => '123 Test Street',
            'address_2' => 'Suite 456',
            'city' => 'Test City',
            'state' => 'TC',
            'postcode' => '12345',
            'country' => 'US',
            'email' => '<EMAIL>',
            'phone' => '555-0123'
        ],
        'shipping' => [
            'shipping_id' => 'TEST_SHIPPING_' . time(),
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'company_name' => 'Test Shipping Co',
            'address_1' => '789 Shipping Ave',
            'address_2' => '',
            'city' => 'Ship City',
            'state' => 'SC',
            'postcode' => '67890',
            'country' => 'US'
        ],
        'line_items' => [
            [
                'product_id' => 101,
                'quantity' => 2
            ],
            [
                'product_id' => 202,
                'quantity' => 1
            ],
            [
                'product_id' => 303,
                'quantity' => 3
            ]
        ]
    ];
    
    echo "<p>Testing with sample data...</p>";
    echo "<div style='background: #f9f9f9; padding: 10px; margin: 10px 0; border-radius: 4px;'>";
    echo "<strong>Test Data Summary:</strong><br>";
    echo "Request ID: {$test_request_id}<br>";
    echo "Order Total: {$test_order_data['total']}<br>";
    echo "Purchase Order: {$test_order_data['purchase_order']}<br>";
    echo "Billing Company: {$test_order_data['billing']['company_name']}<br>";
    echo "Z7_Partner_no: {$test_order_data['billing']['Z7_Partner_no']}<br>";
    echo "Line Items: " . count($test_order_data['line_items']) . "<br>";
    echo "</div>";
    
    // Test the save function
    $save_result = sap_cart_save_to_database($test_order_data, $test_request_id);
    
    if (is_wp_error($save_result)) {
        echo "<p style='color: red;'>❌ Save function failed!</p>";
        echo "<p><strong>Error:</strong> " . $save_result->get_error_message() . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Save function successful!</p>";
        echo "<p><strong>Record ID:</strong> {$save_result['record_id']}</p>";
        echo "<p><strong>Action:</strong> {$save_result['action']}</p>";
        
        // Verify the saved data
        $saved_record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE request_id = %s",
            $test_request_id
        ));
        
        if ($saved_record) {
            echo "<p style='color: green;'>✅ Data verification successful!</p>";
            echo "<div style='background: #f0f8ff; padding: 10px; margin: 10px 0; border-radius: 4px;'>";
            echo "<strong>Saved Record Details:</strong><br>";
            echo "ID: {$saved_record->id}<br>";
            echo "Request ID: {$saved_record->request_id}<br>";
            echo "Order Status: {$saved_record->order_status}<br>";
            echo "Total: {$saved_record->total}<br>";
            echo "Currency: {$saved_record->currency}<br>";
            echo "Purchase Order: {$saved_record->purchase_order}<br>";
            echo "Billing Company: {$saved_record->billing_company_name}<br>";
            echo "Z7_Partner_no: {$saved_record->billing_z7_partner_no}<br>";
            echo "Company Code: {$saved_record->billing_company_code}<br>";
            echo "Line Items Count: {$saved_record->line_items_count}<br>";
            echo "Processing Status: {$saved_record->processing_status}<br>";
            echo "Created At: {$saved_record->created_at}<br>";
            echo "</div>";
            
            // Show line items JSON
            if (!empty($saved_record->line_items)) {
                $line_items = json_decode($saved_record->line_items, true);
                echo "<p><strong>Line Items (JSON):</strong></p>";
                echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 12px;'>";
                echo wp_json_encode($line_items, JSON_PRETTY_PRINT);
                echo "</pre>";
            }
            
            // Clean up test data
            $wpdb->delete($table_name, ['request_id' => $test_request_id], ['%s']);
            echo "<p style='color: blue;'>🧹 Test data cleaned up</p>";
            
        } else {
            echo "<p style='color: red;'>❌ Could not retrieve saved record!</p>";
        }
    }
    
} else {
    echo "<p style='color: red;'>❌ sap_cart_save_to_database() function not found!</p>";
    echo "<p>Make sure sap-cart.php is loaded and activated</p>";
}

// Test 3: Test the complete processing function
echo "<h3>Test 3: Complete Processing Function Test</h3>";

if (function_exists('sap_cart_process_data')) {
    echo "<p style='color: green;'>✅ sap_cart_process_data() function exists</p>";
    
    $test_request_id_2 = 'PROCESS_TEST_' . time() . '_' . rand(1000, 9999);
    $process_result = sap_cart_process_data($test_order_data, $test_request_id_2);
    
    if (is_wp_error($process_result)) {
        echo "<p style='color: red;'>❌ Process function failed!</p>";
        echo "<p><strong>Error:</strong> " . $process_result->get_error_message() . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Process function successful!</p>";
        echo "<div style='background: #f0f8ff; padding: 10px; margin: 10px 0; border-radius: 4px;'>";
        echo "<strong>Process Result:</strong><br>";
        echo "Processed: " . ($process_result['processed'] ? 'Yes' : 'No') . "<br>";
        echo "Items Processed: {$process_result['items_processed']}<br>";
        echo "Processing Time: {$process_result['processing_time']}<br>";
        if (isset($process_result['database_record_id'])) {
            echo "Database Record ID: {$process_result['database_record_id']}<br>";
        }
        echo "</div>";
        
        // Clean up test data
        $wpdb->delete($table_name, ['request_id' => $test_request_id_2], ['%s']);
        echo "<p style='color: blue;'>🧹 Process test data cleaned up</p>";
    }
} else {
    echo "<p style='color: red;'>❌ sap_cart_process_data() function not found!</p>";
}

// Test 4: Show recent records
echo "<h3>Test 4: Recent Records</h3>";
$recent_records = $wpdb->get_results(
    "SELECT id, request_id, order_status, total, billing_company_name, line_items_count, processing_status, created_at 
     FROM {$table_name} 
     ORDER BY created_at DESC 
     LIMIT 5"
);

if (empty($recent_records)) {
    echo "<p style='color: blue;'>ℹ️ No records found in the table</p>";
} else {
    echo "<p style='color: green;'>✅ Found " . count($recent_records) . " recent records</p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Request ID</th><th>Status</th><th>Total</th><th>Company</th><th>Items</th><th>Processing</th><th>Created</th></tr>";
    foreach ($recent_records as $record) {
        echo "<tr>";
        echo "<td>{$record->id}</td>";
        echo "<td style='font-family: monospace; font-size: 11px;'>{$record->request_id}</td>";
        echo "<td>{$record->order_status}</td>";
        echo "<td>{$record->total}</td>";
        echo "<td>{$record->billing_company_name}</td>";
        echo "<td>{$record->line_items_count}</td>";
        echo "<td>{$record->processing_status}</td>";
        echo "<td>{$record->created_at}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>🎉 Testing Complete!</h3>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>✅ Database table is ready and functional</li>";
echo "<li>✅ Save function works correctly</li>";
echo "<li>✅ All data fields are properly stored</li>";
echo "<li>✅ JSON data is preserved</li>";
echo "<li>✅ Processing status tracking works</li>";
echo "</ul>";

echo "<p><strong>What happens now:</strong></p>";
echo "<ol>";
echo "<li>When users visit the cart page, data is automatically collected</li>";
echo "<li>Data is sent to the SAP Cart API endpoint</li>";
echo "<li>The endpoint processes and saves the data to the database</li>";
echo "<li>All activity is logged for monitoring</li>";
echo "<li>You can query the database to see all cart submissions</li>";
echo "</ol>";

echo "<p><strong>Useful Queries:</strong></p>";
echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 4px;'>";
echo "-- View all cart submissions today\n";
echo "SELECT * FROM {$table_name} WHERE DATE(created_at) = CURDATE();\n\n";
echo "-- Count submissions by company\n";
echo "SELECT billing_company_name, COUNT(*) as submissions \n";
echo "FROM {$table_name} \n";
echo "GROUP BY billing_company_name \n";
echo "ORDER BY submissions DESC;\n\n";
echo "-- View line items for a specific submission\n";
echo "SELECT request_id, line_items, line_items_count \n";
echo "FROM {$table_name} \n";
echo "WHERE line_items_count > 0 \n";
echo "ORDER BY created_at DESC;";
echo "</pre>";
?>
