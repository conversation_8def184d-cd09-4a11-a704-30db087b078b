<?php
/**
 * Test script for Z7_Partner_no field functionality
 * This script tests the new Z7_Partner_no field in the SAP SoldTo integration
 */

// Only run this in WordPress environment
if (!defined('ABSPATH')) {
    // For testing outside WordPress, you can define ABSPATH
    // define('ABSPATH', '/path/to/your/wordpress/');
    // require_once(ABSPATH . 'wp-config.php');
    die('This script must be run within WordPress environment');
}

echo "<h2>🧪 Testing Z7_Partner_no Field Integration</h2>";

// Test 1: Check if the column exists in the database
echo "<h3>Test 1: Database Column Check</h3>";
global $wpdb;
$table_name = $wpdb->prefix . 'sap_soldto_customers';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
if (!$table_exists) {
    echo "<p style='color: red;'>❌ Table {$table_name} does not exist!</p>";
    echo "<p>Please create the table using create-soldto-table.sql</p>";
} else {
    echo "<p style='color: green;'>✅ Table {$table_name} exists</p>";
    
    // Check if Z7_Partner_no column exists
    $column_exists = $wpdb->get_results($wpdb->prepare(
        "SHOW COLUMNS FROM {$table_name} LIKE %s",
        'Z7_Partner_no'
    ));
    
    if (empty($column_exists)) {
        echo "<p style='color: red;'>❌ Column Z7_Partner_no does not exist!</p>";
        echo "<p>Please run add-z7-partner-no-column.sql to add the column</p>";
    } else {
        echo "<p style='color: green;'>✅ Column Z7_Partner_no exists</p>";
        
        // Show column details
        $column_info = $column_exists[0];
        echo "<p><strong>Column Details:</strong></p>";
        echo "<ul>";
        echo "<li>Type: " . $column_info->Type . "</li>";
        echo "<li>Null: " . $column_info->Null . "</li>";
        echo "<li>Default: " . ($column_info->Default ?? 'NULL') . "</li>";
        echo "</ul>";
    }
}

// Test 2: Test data insertion with Z7_Partner_no
echo "<h3>Test 2: Data Insertion Test</h3>";
if ($table_exists && !empty($column_exists)) {
    $test_customer_id = 'TEST_Z7_' . time();
    $test_z7_partner = 'Z7_PARTNER_' . time();
    
    $test_data = [
        'customer_id' => $test_customer_id,
        'company_code' => 'TEST',
        'country_code' => 'US',
        'price_group' => 'STANDARD',
        'Z7_Partner_no' => $test_z7_partner,
        'company' => 'Test Company for Z7',
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ];
    
    $result = $wpdb->insert(
        $table_name,
        $test_data,
        ['%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s']
    );
    
    if ($result !== false) {
        echo "<p style='color: green;'>✅ Test record inserted successfully</p>";
        echo "<p><strong>Test Data:</strong></p>";
        echo "<ul>";
        echo "<li>Customer ID: {$test_customer_id}</li>";
        echo "<li>Z7_Partner_no: {$test_z7_partner}</li>";
        echo "</ul>";
        
        // Verify the data was inserted correctly
        $inserted_record = $wpdb->get_row($wpdb->prepare(
            "SELECT customer_id, Z7_Partner_no, company FROM {$table_name} WHERE customer_id = %s",
            $test_customer_id
        ));
        
        if ($inserted_record) {
            echo "<p style='color: green;'>✅ Data verification successful</p>";
            echo "<p><strong>Retrieved Data:</strong></p>";
            echo "<ul>";
            echo "<li>Customer ID: {$inserted_record->customer_id}</li>";
            echo "<li>Z7_Partner_no: {$inserted_record->Z7_Partner_no}</li>";
            echo "<li>Company: {$inserted_record->company}</li>";
            echo "</ul>";
            
            // Clean up test data
            $wpdb->delete($table_name, ['customer_id' => $test_customer_id], ['%s']);
            echo "<p style='color: blue;'>🧹 Test data cleaned up</p>";
        } else {
            echo "<p style='color: red;'>❌ Could not retrieve inserted data</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to insert test record</p>";
        echo "<p>Error: " . $wpdb->last_error . "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Skipping insertion test - table or column not ready</p>";
}

// Test 3: Test the fetch_customer_ids_from_database function
echo "<h3>Test 3: Customer Fetch Function Test</h3>";
if (function_exists('fetch_customer_ids_from_database')) {
    $customers = fetch_customer_ids_from_database();
    echo "<p style='color: green;'>✅ fetch_customer_ids_from_database() function works</p>";
    echo "<p>Found " . count($customers) . " customers in database</p>";
    
    if (!empty($customers)) {
        echo "<p><strong>Sample customer data (first 3 records):</strong></p>";
        $sample_customers = array_slice($customers, 0, 3);
        foreach ($sample_customers as $customer) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>";
            echo "<strong>Customer ID:</strong> " . ($customer['customer_id'] ?? 'N/A') . "<br>";
            echo "<strong>Company Code:</strong> " . ($customer['company_code'] ?? 'N/A') . "<br>";
            echo "<strong>Z7_Partner_no:</strong> " . ($customer['Z7_Partner_no'] ?? 'N/A') . "<br>";
            echo "<strong>Company:</strong> " . ($customer['company'] ?? 'N/A') . "<br>";
            echo "</div>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ fetch_customer_ids_from_database() function not found</p>";
    echo "<p>Make sure customer-price-group.php is loaded</p>";
}

echo "<h3>🎉 Testing Complete!</h3>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>✅ Database table structure updated with Z7_Partner_no field</li>";
echo "<li>✅ SAP SoldTo API will now accept and store Z7_Partner_no data</li>";
echo "<li>✅ Customer dropdown functions include Z7_Partner_no in queries</li>";
echo "<li>✅ Field is database-only (not displayed in UI as requested)</li>";
echo "</ul>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Run the SQL scripts if you haven't already:";
echo "<ul><li>create-soldto-table.sql (for new installations)</li>";
echo "<li>add-z7-partner-no-column.sql (for existing installations)</li></ul></li>";
echo "<li>Test the SAP API with Z7_Partner_no data in the JSON payload</li>";
echo "<li>Verify data is being stored correctly in the database</li>";
echo "</ol>";
?>
