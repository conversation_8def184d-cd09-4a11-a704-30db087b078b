<?php
/**
 * SAP Cart Data Processor
 * Separate file for handling cart data processing logic
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Main cart data processor
 */
class SAP_Cart_Processor {
    
    /**
     * Process incoming cart data
     */
    public static function process_cart_data( $data, $request_id = null ) {
        error_log( "🔄 SAP Cart Processor: Starting data processing" );
        
        if ( empty( $data['order'] ) ) {
            return [
                'success' => false,
                'error' => 'No order data provided'
            ];
        }
        
        $order_data = $data['order'];
        
        // Process different aspects of the data
        $results = [
            'billing_processed' => self::process_billing_data( $order_data['billing'] ?? [] ),
            'shipping_processed' => self::process_shipping_data( $order_data['shipping'] ?? [] ),
            'items_processed' => self::process_line_items( $order_data['line_items'] ?? [] ),
            'custom_fields_processed' => self::process_custom_fields( $order_data ),
        ];
        
        // Log the processing results
        error_log( "✅ SAP Cart Processor: Processing complete" );
        error_log( "📊 Results: " . wp_json_encode( $results ) );
        
        return [
            'success' => true,
            'processed_at' => current_time( 'mysql' ),
            'results' => $results
        ];
    }
    
    /**
     * Process billing data
     */
    private static function process_billing_data( $billing_data ) {
        if ( empty( $billing_data ) ) {
            return ['status' => 'no_data'];
        }
        
        // TODO: Add your billing processing logic here
        // Examples:
        // - Validate billing information
        // - Update customer records
        // - Send to accounting system
        
        error_log( "💳 Processing billing for: " . ( $billing_data['company_name'] ?? 'Unknown' ) );
        error_log( "💳 Z7_Partner_no: " . ( $billing_data['Z7_Partner_no'] ?? 'Not provided' ) );
        error_log( "💳 Company Code: " . ( $billing_data['company_code'] ?? 'Not provided' ) );
        
        return [
            'status' => 'processed',
            'billing_id' => $billing_data['billing_id'] ?? null,
            'company_code' => $billing_data['company_code'] ?? null,
            'Z7_Partner_no' => $billing_data['Z7_Partner_no'] ?? null
        ];
    }
    
    /**
     * Process shipping data
     */
    private static function process_shipping_data( $shipping_data ) {
        if ( empty( $shipping_data ) ) {
            return ['status' => 'no_data'];
        }
        
        // TODO: Add your shipping processing logic here
        // Examples:
        // - Validate shipping address
        // - Calculate shipping costs
        // - Update logistics system
        
        error_log( "🚚 Processing shipping to: " . ( $shipping_data['city'] ?? 'Unknown' ) );
        error_log( "🚚 Shipping ID: " . ( $shipping_data['shipping_id'] ?? 'Not provided' ) );
        
        return [
            'status' => 'processed',
            'shipping_id' => $shipping_data['shipping_id'] ?? null,
            'address_validated' => true // Example result
        ];
    }
    
    /**
     * Process line items
     */
    private static function process_line_items( $line_items ) {
        if ( empty( $line_items ) ) {
            return ['status' => 'no_items'];
        }
        
        $processed_items = [];
        
        foreach ( $line_items as $item ) {
            // TODO: Add your item processing logic here
            // Examples:
            // - Check inventory
            // - Update stock levels
            // - Calculate pricing
            // - Send to fulfillment system
            
            $product_id = $item['product_id'] ?? 0;
            $quantity = $item['quantity'] ?? 0;
            
            error_log( "📦 Processing item: Product ID {$product_id}, Quantity: {$quantity}" );
            
            $processed_items[] = [
                'product_id' => $product_id,
                'quantity' => $quantity,
                'processed' => true,
                'inventory_checked' => true // Example result
            ];
        }
        
        return [
            'status' => 'processed',
            'total_items' => count( $processed_items ),
            'items' => $processed_items
        ];
    }
    
    /**
     * Process custom fields
     */
    private static function process_custom_fields( $order_data ) {
        $custom_fields = [
            'purchase_order' => $order_data['purchase_order'] ?? '',
            'Cust_Req_Ship_Date' => $order_data['Cust_Req_Ship_Date'] ?? '',
            'freight_terms' => $order_data['freight_terms'] ?? '',
            'shipping_conditions' => $order_data['shipping_conditions'] ?? '',
            'shipping_account' => $order_data['shipping_account'] ?? '',
            'end_user' => $order_data['end_user'] ?? '',
            'end_use' => $order_data['end_use'] ?? ''
        ];
        
        // TODO: Add your custom field processing logic here
        // Examples:
        // - Validate field values
        // - Update CRM system
        // - Generate reports
        
        foreach ( $custom_fields as $field => $value ) {
            if ( ! empty( $value ) ) {
                error_log( "🔧 Custom field {$field}: {$value}" );
            }
        }
        
        return [
            'status' => 'processed',
            'fields_processed' => array_filter( $custom_fields ) // Only non-empty fields
        ];
    }
    
    /**
     * Save data to database (example)
     */
    public static function save_to_database( $data, $request_id ) {
        global $wpdb;
        
        // Example: Create a table to store cart data
        $table_name = $wpdb->prefix . 'sap_cart_submissions';
        
        $result = $wpdb->insert(
            $table_name,
            [
                'request_id' => $request_id,
                'cart_data' => wp_json_encode( $data ),
                'submitted_at' => current_time( 'mysql' ),
                'status' => 'received'
            ],
            [ '%s', '%s', '%s', '%s' ]
        );
        
        if ( $result === false ) {
            error_log( "❌ Failed to save cart data to database: " . $wpdb->last_error );
            return false;
        }
        
        error_log( "✅ Cart data saved to database with ID: " . $wpdb->insert_id );
        return $wpdb->insert_id;
    }
    
    /**
     * Send to external API (example)
     */
    public static function send_to_external_api( $data, $api_url, $api_token = null ) {
        $headers = [
            'Content-Type' => 'application/json'
        ];
        
        if ( $api_token ) {
            $headers['Authorization'] = 'Bearer ' . $api_token;
        }
        
        $response = wp_remote_post( $api_url, [
            'body' => wp_json_encode( $data ),
            'headers' => $headers,
            'timeout' => 30
        ] );
        
        if ( is_wp_error( $response ) ) {
            error_log( "❌ Failed to send to external API: " . $response->get_error_message() );
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code( $response );
        $response_body = wp_remote_retrieve_body( $response );
        
        error_log( "📡 External API response: Code {$response_code}" );
        error_log( "📡 Response body: " . $response_body );
        
        return [
            'success' => $response_code >= 200 && $response_code < 300,
            'response_code' => $response_code,
            'response_body' => $response_body
        ];
    }
}

// Hook into the SAP Cart processing
add_filter( 'sap_cart_process_data', function( $data, $request_id ) {
    return SAP_Cart_Processor::process_cart_data( $data, $request_id );
}, 10, 2 );
