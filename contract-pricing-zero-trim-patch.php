<?php
/**
 * Patch for SAP Contract Pricing - Leading Zero Trimming
 * This code should be inserted after line 194 in sap-contract-pricing.php
 */

// Replace this section in sap-contract-pricing.php around line 193-196:
/*
    $material_number = sanitize_text_field( $pricing_item['materialNumber'] );
    $customer_id = sanitize_text_field( $pricing_item['customer_id'] );

    sap_contract_pricing_log_api( "🔍 SAP-Contract-Pricing Processing material {$material_number} for customer {$customer_id} [ID: {$request_id}, Item: {$index}]" );
*/

// With this enhanced version:

    $material_number = sanitize_text_field( $pricing_item['materialNumber'] );
    $customer_id = sanitize_text_field( $pricing_item['customer_id'] );
    
    // Trim leading zeros from material number for consistency
    $original_material_number = $material_number;
    $material_number = ltrim( $material_number, '0' );
    if ( empty( $material_number ) ) {
        $material_number = '0';
    }
    
    // Trim leading zeros from customer ID for consistency
    $original_customer_id = $customer_id;
    $customer_id = ltrim( $customer_id, '0' );
    if ( empty( $customer_id ) ) {
        $customer_id = '0';
    }
    
    // Log transformations if they occurred
    if ( $original_material_number !== $material_number ) {
        error_log( "🔄 SAP-Contract-Pricing: Material number trimmed: '{$original_material_number}' -> '{$material_number}'" );
        sap_contract_pricing_log_api( "🔄 SAP-Contract-Pricing Material number trimmed: '{$original_material_number}' -> '{$material_number}' [ID: {$request_id}, Item: {$index}]" );
    }
    
    if ( $original_customer_id !== $customer_id ) {
        error_log( "🔄 SAP-Contract-Pricing: Customer ID trimmed: '{$original_customer_id}' -> '{$customer_id}'" );
        sap_contract_pricing_log_api( "🔄 SAP-Contract-Pricing Customer ID trimmed: '{$original_customer_id}' -> '{$customer_id}' [ID: {$request_id}, Item: {$index}]" );
    }

    sap_contract_pricing_log_api( "🔍 SAP-Contract-Pricing Processing material {$material_number} for customer {$customer_id} [ID: {$request_id}, Item: {$index}]" );

?>
