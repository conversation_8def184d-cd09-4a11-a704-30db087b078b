<?php
/**
 * Test script for SAP SoldTo zero-trimming functionality
 * This script tests the leading zero removal from customer IDs in user metadata
 */

// Only run this in WordPress environment
if (!defined('ABSPATH')) {
    die('This script must be run within WordPress environment');
}

echo "<h2>🧪 Testing SAP SoldTo Zero-Trimming Functionality</h2>";

// Test 1: Check current user metadata storage
echo "<h3>Test 1: Current User Metadata Analysis</h3>";

global $wpdb;

// Check for users with leading zero customer IDs
$users_with_leading_zeros = $wpdb->get_results("
    SELECT um.user_id, um.meta_value as customer_id, u.user_login
    FROM {$wpdb->usermeta} um
    JOIN {$wpdb->users} u ON um.user_id = u.ID
    WHERE um.meta_key = '_customer' 
    AND um.meta_value REGEXP '^0+[0-9]'
    AND um.meta_value != '0'
    ORDER BY um.user_id
    LIMIT 10
");

if (empty($users_with_leading_zeros)) {
    echo "<p style='color: green;'>✅ No users found with leading zero customer IDs in metadata!</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Found " . count($users_with_leading_zeros) . " users with leading zero customer IDs:</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>User ID</th><th>Username</th><th>Customer ID (with zeros)</th></tr>";
    foreach ($users_with_leading_zeros as $user_data) {
        echo "<tr>";
        echo "<td>{$user_data->user_id}</td>";
        echo "<td>{$user_data->user_login}</td>";
        echo "<td><code>{$user_data->customer_id}</code></td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><em>Run fix-customer-id-leading-zeros.php to fix these.</em></p>";
}

// Test 2: Check database storage
echo "<h3>Test 2: Database Storage Analysis</h3>";

$table_name = $wpdb->prefix . 'sap_soldto_customers';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;

if (!$table_exists) {
    echo "<p style='color: red;'>❌ SAP SoldTo customers table does not exist</p>";
} else {
    echo "<p style='color: green;'>✅ SAP SoldTo customers table exists</p>";
    
    // Check for leading zeros in database
    $db_leading_zeros = $wpdb->get_results("
        SELECT customer_id, company, created_at
        FROM {$table_name}
        WHERE customer_id REGEXP '^0+[0-9]'
        AND customer_id != '0'
        ORDER BY created_at DESC
        LIMIT 10
    ");
    
    if (empty($db_leading_zeros)) {
        echo "<p style='color: green;'>✅ No customer IDs with leading zeros found in database!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Found " . count($db_leading_zeros) . " database records with leading zero customer IDs:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Customer ID</th><th>Company</th><th>Created</th></tr>";
        foreach ($db_leading_zeros as $record) {
            echo "<tr>";
            echo "<td><code>{$record->customer_id}</code></td>";
            echo "<td>{$record->company}</td>";
            echo "<td>{$record->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><em>These should be cleaned up in future API calls.</em></p>";
    }
}

// Test 3: Test the zero-trimming logic
echo "<h3>Test 3: Zero-Trimming Logic Test</h3>";

$test_cases = [
    '0001126769' => '1126769',
    '0000000001' => '1',
    '0000000000' => '0',
    '1234567890' => '1234567890',
    '0000123ABC' => '123ABC',
    '000000000000000000000000000000000000000000000000000000000000000001' => '1'
];

echo "<p><strong>Testing customer ID trimming logic:</strong></p>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Original Customer ID</th><th>Expected Result</th><th>Actual Result</th><th>Status</th></tr>";

foreach ($test_cases as $original => $expected) {
    // Simulate the trimming logic from the updated code
    $trimmed = ltrim($original, '0');
    if (empty($trimmed)) {
        $trimmed = '0';
    }
    
    $status = ($trimmed === $expected) ? '✅ Pass' : '❌ Fail';
    $status_color = ($trimmed === $expected) ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td><code>{$original}</code></td>";
    echo "<td><code>{$expected}</code></td>";
    echo "<td><code>{$trimmed}</code></td>";
    echo "<td style='color: {$status_color};'>{$status}</td>";
    echo "</tr>";
}
echo "</table>";

// Test 4: Check if SoldTo functions exist
echo "<h3>Test 4: SAP SoldTo Functions Check</h3>";

$soldto_functions = [
    'sap_soldto_endpoint',
    'sap_update_customer_database',
    'sap_update_user_metadata'
];

foreach ($soldto_functions as $function_name) {
    if (function_exists($function_name)) {
        echo "<p style='color: green;'>✅ Function {$function_name}() exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Function {$function_name}() NOT found</p>";
    }
}

// Test 5: Sample data processing simulation
echo "<h3>Test 5: Sample Data Processing Simulation</h3>";

$sample_soldto_data = [
    'soldTo' => [
        'customerId' => '0001126769',
        'companyCode' => '1000',
        'countryCode' => 'US',
        'priceGroup' => 'STANDARD'
    ],
    'billingAddress' => [
        'company' => 'Test Company Inc',
        'address' => [
            'line1' => '123 Test Street',
            'city' => 'Test City',
            'postcode' => '12345'
        ]
    ]
];

echo "<p><strong>Sample SoldTo data:</strong></p>";
echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 4px;'>";
echo wp_json_encode($sample_soldto_data, JSON_PRETTY_PRINT);
echo "</pre>";

// Simulate the processing
$original_customer_id = $sample_soldto_data['soldTo']['customerId'];
$trimmed_customer_id = ltrim($original_customer_id, '0');
if (empty($trimmed_customer_id)) {
    $trimmed_customer_id = '0';
}

echo "<p><strong>Processing simulation:</strong></p>";
echo "<ul>";
echo "<li>Original Customer ID: <code>{$original_customer_id}</code></li>";
echo "<li>Trimmed Customer ID: <code>{$trimmed_customer_id}</code></li>";
echo "<li>Database storage: <code>{$trimmed_customer_id}</code> ✅</li>";
echo "<li>User metadata storage: <code>{$trimmed_customer_id}</code> ✅</li>";
echo "</ul>";

if ($original_customer_id !== $trimmed_customer_id) {
    echo "<p style='color: green;'>✅ Zero-trimming would be applied and logged</p>";
} else {
    echo "<p style='color: blue;'>ℹ️ No zero-trimming needed for this customer ID</p>";
}

// Test 6: Show before/after comparison
echo "<h3>Test 6: Before/After Comparison</h3>";

echo "<div style='display: flex; gap: 20px;'>";

echo "<div style='flex: 1; background: #ffe6e6; padding: 15px; border-radius: 4px;'>";
echo "<h4>❌ Before (With Leading Zeros)</h4>";
echo "<p><strong>Database:</strong> <code>0001126769</code></p>";
echo "<p><strong>User Metadata:</strong> <code>0001126769</code></p>";
echo "<p><strong>Issues:</strong></p>";
echo "<ul>";
echo "<li>Inconsistent data format</li>";
echo "<li>Cart API lookups might fail</li>";
echo "<li>Customer matching problems</li>";
echo "</ul>";
echo "</div>";

echo "<div style='flex: 1; background: #e6ffe6; padding: 15px; border-radius: 4px;'>";
echo "<h4>✅ After (Zero-Trimmed)</h4>";
echo "<p><strong>Database:</strong> <code>1126769</code></p>";
echo "<p><strong>User Metadata:</strong> <code>1126769</code></p>";
echo "<p><strong>Benefits:</strong></p>";
echo "<ul>";
echo "<li>Consistent data format</li>";
echo "<li>Reliable customer lookups</li>";
echo "<li>Proper API integration</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<h3>🎉 Testing Complete!</h3>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>✅ Zero-trimming logic implemented in SAP SoldTo API</li>";
echo "<li>✅ Customer IDs are now trimmed before storing in user metadata</li>";
echo "<li>✅ Database storage already had zero-trimming</li>";
echo "<li>✅ Consistent with SAP Inventory API implementation</li>";
echo "</ul>";

echo "<p><strong>What was changed:</strong></p>";
echo "<ol>";
echo "<li>Added zero-trimming logic in <code>sap_update_user_metadata()</code> function</li>";
echo "<li>Customer IDs are trimmed before storing in <code>_customer</code> metadata</li>";
echo "<li>Added logging to show original → trimmed transformation</li>";
echo "<li>Created migration script to fix existing data</li>";
echo "</ol>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Run <code>fix-customer-id-leading-zeros.php</code> to fix existing user metadata</li>";
echo "<li>Test new SoldTo API calls to verify zero-trimming works</li>";
echo "<li>Verify Cart API can now find customers correctly</li>";
echo "</ol>";

echo "<p><strong>Files:</strong></p>";
echo "<ul>";
echo "<li><code>sap-soldto.php</code> - Updated with zero-trimming for user metadata</li>";
echo "<li><code>fix-customer-id-leading-zeros.php</code> - Migration script for existing data</li>";
echo "<li><code>test-soldto-zero-trim.php</code> - This test script</li>";
echo "</ul>";
?>
